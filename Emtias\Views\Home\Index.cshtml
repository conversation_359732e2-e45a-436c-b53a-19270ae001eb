@{
    ViewData["Title"] = "لوحة التحكم الرئيسية";
    var stats = ViewBag.Stats;
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-speedometer2 text-primary me-2"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                    <p class="text-muted mb-0">نظرة عامة على إحصائيات النظام</p>
                </div>
                <div class="text-muted">
                    <i class="bi bi-calendar3 me-1"></i>
                    @DateTime.Now.ToString("yyyy/MM/dd - HH:mm")
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards Row 1 -->
    <div class="row mb-4">
        <!-- Total Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي المبيعات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalSales.ToString("N0") ر.س</div>
                            <div class="text-xs text-success">
                                <i class="bi bi-arrow-up me-1"></i>
                                @stats.MonthlySales.ToString("N0") ر.س هذا الشهر
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-dollar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Users Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                عدد المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalUsers</div>
                            <div class="text-xs text-success">
                                <i class="bi bi-person-plus me-1"></i>
                                @stats.NewUsersThisMonth جديد هذا الشهر
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sold Packages Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الحزم المبيعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.SoldPackages</div>
                            <div class="text-xs text-success">
                                <i class="bi bi-bag-check me-1"></i>
                                @stats.SoldPackagesThisMonth هذا الشهر
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-box-seam fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalCategories</div>
                            <div class="text-xs text-success">
                                <i class="bi bi-check-circle me-1"></i>
                                @stats.CategoriesWithRestaurants منها يحتوي على مطاعم
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-grid-3x3-gap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards Row 2 -->
    <div class="row mb-4">
        <!-- Restaurants Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المطاعم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalRestaurants</div>
                            <div class="text-xs">
                                <span class="text-success">@stats.ActiveRestaurants نشط</span>
                                @if (stats.DeletedRestaurants > 0)
                                {
                                    <span class="text-danger ms-2">@stats.DeletedRestaurants محذوف</span>
                                }
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-shop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المنتجات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalProducts</div>
                            <div class="text-xs">
                                <span class="text-success">@stats.ActiveProducts نشط</span>
                                <span class="text-warning ms-2">@stats.NewProducts جديد</span>
                                @if (stats.InactiveProducts > 0)
                                {
                                    <span class="text-muted ms-2">@stats.InactiveProducts غير نشط</span>
                                }
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Offers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                العروض
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@stats.TotalOffers</div>
                            <div class="text-xs">
                                <span class="text-success">@stats.ActiveOffers نشط</span>
                                @if (stats.ExpiredOffers > 0)
                                {
                                    <span class="text-danger ms-2">@stats.ExpiredOffers منتهي</span>
                                }
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-tag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
    </div>

   

      

       
    <!-- Advanced Charts Section -->
    <div class="row mb-4">
        <!-- User Growth Chart -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-graph-up me-2"></i>
                        نمو المستخدمين (آخر 6 أشهر)
                    </h6>
                    
                </div>
                <div class="card-body">
                    <div id="userGrowthChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- Sales Growth Chart -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="bi bi-currency-dollar me-2"></i>
                        نمو المبيعات (آخر 6 أشهر)
                    </h6>
                 
                </div>
                <div class="card-body">
                    <div id="salesGrowthChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics Charts -->
    <div class="row mb-4">
        <!-- Category Distribution Chart -->
        <div class="col-xl-4 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="bi bi-pie-chart me-2"></i>
                        توزيع المبيعات حسب الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <div id="categoryChart" style="height: 280px;"></div>
                </div>
            </div>
        </div>

        <!-- Growth Rate Chart -->
        <div class="col-xl-4 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="bi bi-speedometer2 me-2"></i>
                        معدل النمو الشهري
                    </h6>
                </div>
                <div class="card-body">
                    <div id="growthRateChart" style="height: 280px;"></div>
                </div>
            </div>
        </div>

        <!-- Daily Performance Chart -->
        <div class="col-xl-4 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="bi bi-activity me-2"></i>
                        الأداء اليومي (آخر 7 أيام)
                    </h6>
                </div>
                <div class="card-body">
                    <div id="dailyPerformanceChart" style="height: 280px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning me-2"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Catfory")" class="btn btn-outline-primary w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة قسم جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Restaurant")" class="btn btn-outline-success w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة مطعم جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Product")" class="btn btn-outline-info w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Offer")" class="btn btn-outline-warning w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                إضافة عرض جديد
                            </a>
                        </div>
                          <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Employee")" class="btn btn-outline-dark w-100">
                                <i class="bi bi-person-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "User")" class="btn btn-outline-success w-100">
                                <i class="bi bi-person-plus-fill me-2"></i>
                                إضافة مستخدم جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Content")" class="btn btn-outline-info w-100">
                                <i class="bi bi-file-text me-2"></i>
                                إضافة محتوى جديد
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Create", "Ticket")" class="btn btn-outline-primary w-100">
                                <i class="bi bi-ticket-perforated me-2"></i>
                                إضافة تذكرة جديدة
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-6 mb-3">
                            <a href="@Url.Action("Index", "Ticket")" class="btn btn-outline-success w-100">
                                <i class="bi bi-list-check me-2"></i>
                                إدارة التذاكر
                            </a>
                        </div>

                    </div>
                    
                </div>
            </div>
        </div>
    </div>

    
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .border-left-secondary {
        border-left: 0.25rem solid #858796 !important;
    }

    .border-left-dark {
        border-left: 0.25rem solid #5a5c69 !important;
    }

    /* KPI Cards Animation */
    .card {
        transition: all 0.3s ease-in-out;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }

    /* Currency formatting */
    .currency-large {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1cc88a;
    }

    /* Badge styling */
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-gray-300 {
        color: #dddfeb !important;
    }

    .fa-2x {
        font-size: 2em;
    }

    .card {
        transition: all 0.3s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .btn {
        transition: all 0.2s ease-in-out;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    .progress-bar {
        transition: width 0.6s ease;
    }

    .text-xs {
        font-size: 0.75rem;
    }

    .text-sm {
        font-size: 0.875rem;
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    .no-gutters {
        margin-right: 0;
        margin-left: 0;
    }

    .no-gutters > .col,
    .no-gutters > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }

    /* Custom animations */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translate3d(0, 40px, 0);
        }
        to {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    .card {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Chart Area */
    .chart-area {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .chart-area canvas {
        height: 100% !important;
        width: 100% !important;
    }



    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .h5 {
            font-size: 1.1rem;
        }

        .fa-2x {
            font-size: 1.5em;
        }

        .card-body {
            padding: 1rem;
        }

        .chart-area {
            height: 250px;
        }
    }

   
</style>

<!-- ApexCharts CDN -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات الرسوم البيانية من الخادم
        const userGrowthData = @Html.Raw(Json.Serialize(ViewBag.Stats.UserGrowthData));
        const salesGrowthData = @Html.Raw(Json.Serialize(ViewBag.Stats.SalesGrowthData));

        // رسم بياني لنمو المستخدمين - ApexCharts
        const userGrowthOptions = {
            series: [{
                name: 'المستخدمين الجدد',
                data: userGrowthData.map(item => item.users || 0)
            }],
            chart: {
                type: 'area',
                height: 350,
                fontFamily: 'Cairo, sans-serif',
                toolbar: {
                    show: true
                },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            dataLabels: {
                enabled: true,
                style: {
                    fontSize: '12px',
                    fontFamily: 'Cairo, sans-serif',
                    fontWeight: 'bold'
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.1
                }
            },
            colors: ['#4e73df'],
            xaxis: {
                categories: userGrowthData.map(item => {
                    const date = new Date(item.month + '-01');
                    return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
                }),
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    },
                    formatter: function (val) {
                        return val.toLocaleString('ar-SA');
                    }
                }
            },
            tooltip: {
                theme: 'light',
                style: {
                    fontSize: '12px',
                    fontFamily: 'Cairo, sans-serif'
                },
                y: {
                    formatter: function (val) {
                        return val.toLocaleString('ar-SA') + ' مستخدم جديد';
                    }
                }
            },
            grid: {
                borderColor: '#e7e7e7',
                strokeDashArray: 5
            }
        };
        // رسم بياني لنمو المبيعات - ApexCharts
        const salesGrowthOptions = {
            series: [{
                name: 'المبيعات الشهرية',
                data: salesGrowthData.map(item => item.sales || 0)
            }],
            chart: {
                type: 'bar',
                height: 350,
                fontFamily: 'Cairo, sans-serif',
                toolbar: {
                    show: true
                }
            },
            plotOptions: {
                bar: {
                    borderRadius: 8,
                    columnWidth: '60%',
                    dataLabels: {
                        position: 'top'
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return val.toLocaleString('ar-SA') + ' ر.س';
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    fontFamily: 'Cairo, sans-serif'
                }
            },
            colors: ['#1cc88a'],
            xaxis: {
                categories: salesGrowthData.map(item => {
                    const date = new Date(item.month + '-01');
                    return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
                }),
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    },
                    formatter: function (val) {
                        return val.toLocaleString('ar-SA') + ' ر.س';
                    }
                }
            },
            tooltip: {
                theme: 'light',
                style: {
                    fontSize: '12px',
                    fontFamily: 'Cairo, sans-serif'
                },
                y: {
                    formatter: function (val) {
                        return val.toLocaleString('ar-SA') + ' ريال سعودي';
                    }
                }
            },
            grid: {
                borderColor: '#e7e7e7',
                strokeDashArray: 5
            }
        };

        // رسم بياني دائري لتوزيع الفئات
        const categoryOptions = {
            series: [44, 55, 13, 43, 22],
            chart: {
                type: 'donut',
                height: 280,
                fontFamily: 'Cairo, sans-serif'
            },
            labels: ['المطاعم', 'المنتجات', 'العروض', 'الحجوزات', 'أخرى'],
            colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'الإجمالي',
                                fontSize: '16px',
                                fontFamily: 'Cairo, sans-serif'
                            }
                        }
                    }
                }
            },
            dataLabels: {
                enabled: true,
                style: {
                    fontFamily: 'Cairo, sans-serif'
                }
            },
            legend: {
                position: 'bottom',
                fontFamily: 'Cairo, sans-serif'
            }
        };

        // رسم بياني لمعدل النمو
        const growthRateOptions = {
            series: [76],
            chart: {
                type: 'radialBar',
                height: 280,
                fontFamily: 'Cairo, sans-serif'
            },
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '70%'
                    },
                    dataLabels: {
                        name: {
                            fontSize: '17px',
                            fontFamily: 'Cairo, sans-serif'
                        },
                        value: {
                            formatter: function(val) {
                                return parseInt(val) + '%';
                            },
                            fontSize: '36px',
                            fontFamily: 'Cairo, sans-serif'
                        }
                    }
                }
            },
            colors: ['#f6c23e'],
            labels: ['معدل النمو']
        };

        // رسم بياني للأداء اليومي
        const dailyPerformanceOptions = {
            series: [{
                name: 'الطلبات',
                data: [31, 40, 28, 51, 42, 109, 100]
            }, {
                name: 'المبيعات',
                data: [11, 32, 45, 32, 34, 52, 41]
            }],
            chart: {
                type: 'line',
                height: 280,
                fontFamily: 'Cairo, sans-serif'
            },
            stroke: {
                width: [3, 3],
                curve: 'smooth'
            },
            colors: ['#e74a3b', '#36b9cc'],
            xaxis: {
                categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            legend: {
                position: 'top',
                fontFamily: 'Cairo, sans-serif'
            }
        };

        // إنشاء الرسوم البيانية
        const userChart = new ApexCharts(document.querySelector("#userGrowthChart"), userGrowthOptions);
        const salesChart = new ApexCharts(document.querySelector("#salesGrowthChart"), salesGrowthOptions);
        const categoryChart = new ApexCharts(document.querySelector("#categoryChart"), categoryOptions);
        const growthChart = new ApexCharts(document.querySelector("#growthRateChart"), growthRateOptions);
        const dailyChart = new ApexCharts(document.querySelector("#dailyPerformanceChart"), dailyPerformanceOptions);

        // عرض الرسوم البيانية
        userChart.render();
        salesChart.render();
        categoryChart.render();
        growthChart.render();
        dailyChart.render();

        // وظيفة تصدير الرسوم البيانية
        window.exportChart = function(chartId, format = 'png') {
            const charts = {
                'userGrowthChart': userChart,
                'salesGrowthChart': salesChart,
                'categoryChart': categoryChart,
                'growthRateChart': growthChart,
                'dailyPerformanceChart': dailyChart
            };

            if (charts[chartId]) {
                charts[chartId].dataURI({
                    format: format
                }).then((uri) => {
                    const link = document.createElement('a');
                    link.href = uri.imgURI;
                    link.download = chartId + '.' + format;
                    link.click();
                });
            }
        };

        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const now = new Date();
            const timeString = now.getFullYear() + '/' +
                             String(now.getMonth() + 1).padStart(2, '0') + '/' +
                             String(now.getDate()).padStart(2, '0') + ' - ' +
                             String(now.getHours()).padStart(2, '0') + ':' +
                             String(now.getMinutes()).padStart(2, '0');

            const timeElement = document.querySelector('.text-muted i.bi-calendar3').parentElement;
            if (timeElement) {
                timeElement.innerHTML = '<i class="bi bi-calendar3 me-1"></i>' + timeString;
            }
        }, 60000);

        // إضافة تأثيرات hover للبطاقات
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // إضافة tooltips للأشرطة التقدمية
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            if (bar.title) {
                bar.setAttribute('data-bs-toggle', 'tooltip');
                bar.setAttribute('data-bs-placement', 'top');
            }
        });

        // تفعيل Bootstrap tooltips
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });
</script>
