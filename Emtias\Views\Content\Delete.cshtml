@model Emtias.Models.Content

@{
    ViewData["Title"] = "حذف المحتوى";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>حذف المحتوى</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المحتوى</a></li>
            <li class="breadcrumb-item active">حذف</li>
        </ol>
    </nav>
</div>

<!-- Main Content -->
<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Alert -->
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h4 class="alert-heading">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    تحذير: عملية حذف
                </h4>
                <p class="mb-0">
                    هل أنت متأكد من رغبتك في حذف هذا المحتوى؟ هذا الإجراء لا يمكن التراجع عنه.
                </p>
            </div>

            <!-- Content Information Card -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-danger">
                        <i class="bi bi-file-text me-2"></i>
                        معلومات المحتوى المراد حذفه
                    </h5>
                    <hr>

                    <!-- Content Details -->
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-hash me-2"></i>
                                        رقم المحتوى
                                    </h6>
                                    <p class="card-text"><strong>#@Model.Id</strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-type me-2"></i>
                                        العنوان
                                    </h6>
                                    <p class="card-text"><strong>@Model.Title</strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-calendar-plus me-2"></i>
                                        تاريخ الإنشاء
                                    </h6>
                                    <p class="card-text mb-1">
                                        <i class="bi bi-calendar me-1"></i>
                                        @Model.CreatedAt.ToString("dd/MM/yyyy")
                                    </p>
                                    <p class="card-text text-muted mb-0">
                                        <i class="bi bi-clock me-1"></i>
                                        @Model.CreatedAt.ToString("hh:mm tt")
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        آخر تحديث
                                    </h6>
                                    @if (Model.UpdatedAt.HasValue)
                                    {
                                        <p class="card-text mb-1">
                                            <i class="bi bi-calendar me-1"></i>
                                            @Model.UpdatedAt.Value.ToString("dd/MM/yyyy")
                                        </p>
                                        <p class="card-text text-muted mb-0">
                                            <i class="bi bi-clock me-1"></i>
                                            @Model.UpdatedAt.Value.ToString("hh:mm tt")
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="card-text mb-0">
                                            <span class="badge bg-secondary">لم يتم التحديث</span>
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-file-text me-2"></i>
                                        معاينة المحتوى
                                    </h6>
                                    <div class="content-preview p-3" style="max-height: 300px; overflow-y: auto; background-color: white; border-radius: 8px; border: 1px solid #dee2e6;">
                                        @Html.Raw(Model.Content1)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Form -->
                    <form asp-action="Delete" class="mt-4">
                        <input type="hidden" asp-for="Id" />
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                إلغاء والعودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-danger" onclick="return confirmDelete()">
                                <i class="bi bi-trash me-1"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <style>
        .alert-danger {
            border-left: 4px solid #dc3545;
        }

        .card {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            color: #012970;
            font-weight: 600;
        }

        .content-preview {
            line-height: 1.6;
            font-size: 14px;
        }

        .content-preview h1,
        .content-preview h2,
        .content-preview h3,
        .content-preview h4,
        .content-preview h5,
        .content-preview h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .content-preview p {
            margin-bottom: 0.75rem;
        }

        .content-preview ul,
        .content-preview ol {
            margin-bottom: 0.75rem;
            padding-right: 1.5rem;
        }

        .content-preview li {
            margin-bottom: 0.25rem;
        }

        .content-preview img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .content-preview table {
            width: 100%;
            margin-bottom: 0.75rem;
            border-collapse: collapse;
            font-size: 13px;
        }

        .content-preview table th,
        .content-preview table td {
            padding: 0.5rem;
            border: 1px solid #dee2e6;
        }

        .content-preview table th {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .btn-danger {
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }
    </style>

    <script>
        // Confirm delete with SweetAlert or native confirm
        function confirmDelete() {
            return confirm('⚠️ تحذير!\n\nهل أنت متأكد تماماً من رغبتك في حذف هذا المحتوى؟\n\nالعنوان: @Model.Title\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم حذف المحتوى نهائياً من قاعدة البيانات.');
        }

        // Add animation on page load
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .alert');
            cards.forEach((card, index) => {
                card.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s both`;
            });
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @@keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
}

