﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModule" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\Emtias.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="outprocess" />
    </system.webServer>
  </location>
</configuration>
<!--ProjectGuid: 79531704-43BF-4F20-947A-9F4339D086AE-->