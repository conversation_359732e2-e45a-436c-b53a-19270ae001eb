@using Emtias.Data
@using Microsoft.AspNetCore.Identity
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@using System.Globalization
@* @using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer localizer *@
@{
    var isRTL = true; //CultureInfo.CurrentCulture.Name.StartsWith("ar");
}
<!DOCTYPE html>
<html lang="@(isRTL ? "ar" : "en")" dir="@(isRTL ? "rtl" : "ltr")">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - appname</title>
    @* <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/HELPWEBAPP.styles.css" asp-append-version="true" /> *@

    <!-- Favicons -->
    <link href="~/assets/img/apple-touch-icon.png" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <!-- <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet"> -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <link href="~/assets/font/stylesheet.css" rel="stylesheet">
    <!-- Vendor CSS Files -->
    <link href="~/assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="~/assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="~/assets/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="~/assets/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="~/assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="~/assets/vendor/simple-datatables/style.css" rel="stylesheet">
    <link href="~/css/sweetalert2.css" rel="stylesheet">

    @* <script src="~/js/jquery-ui.js"></script> *@
    <script src="~/js/sweetalert2.js"></script>
    <script src="~/js/jquery-1.11.3.min.js"></script>
    <!-- Template Main CSS File -->
    @if (isRTL)
    {
        <link href="~/assets/css/stylertl.css" rel="stylesheet" asp-append-version="true">

    }
    else
    {
        <link href="~/assets/css/style.css" rel="stylesheet" asp-append-version="true">

    }
    <link href="~/css/site.css" rel="stylesheet" asp-append-version="true">

</head>
<body>
    @if (SignInManager.IsSignedIn(User) || 1==1)
    {
        <header id="header" class="header fixed-top d-flex align-items-center">

            <div class="d-flex align-items-center justify-content-between">
                <!-- <a href="index.html" class="logo d-flex align-items-center">
                <img src="assets/img/logo.png" alt="">
                <span class="d-none d-lg-block">NiceAdmin</span>
                </a> -->
                <i class="bi bi-list toggle-sidebar-btn"></i>
            </div>

            <!-- End Logo -->
            <nav class="header-nav ms-auto">
                <ul class="d-flex align-items-center">

                    <li class="nav-item dropdown ">

                        <a class="nav-link nav-profile d-flex align-items-center pe-0 ml-1" href="#" data-bs-toggle="dropdown">
                            <span class="d-none d-md-block ps-2">امتياز</span>
                            @* <img src="/assets/img/124.jpg" alt="Profile" class="rounded-circle"> *@

                        </a><!-- End Profile Iamge Icon -->
                    </li><!-- End Profile Nav -->

                    <li class="nav-item dropdown">

                        <a class="nav-link nav-icon" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-bell"></i>
                            <!-- <span class="badge bg-primary badge-number">4</span> -->
                        </a><!-- End Notification Icon -->

                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-arrow notifications">
                            <li class="dropdown-header">
                                You have 4 new notifications
                                <a href="#"><span class="badge rounded-pill bg-primary p-2 ms-2">View all</span></a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>

                            <li class="notification-item">
                                <i class="bi bi-exclamation-circle text-warning"></i>
                                <div>
                                    <h4>Lorem Ipsum</h4>
                                    <p>Quae dolorem earum veritatis oditseno</p>
                                    <p>30 min. ago</p>
                                </div>
                            </li>

                            <li>
                                <hr class="dropdown-divider">
                            </li>

                            <li class="notification-item">
                                <i class="bi bi-x-circle text-danger"></i>
                                <div>
                                    <h4>Atque rerum nesciunt</h4>
                                    <p>Quae dolorem earum veritatis oditseno</p>
                                    <p>1 hr. ago</p>
                                </div>
                            </li>

                            <li>
                                <hr class="dropdown-divider">
                            </li>

                            <li class="notification-item">
                                <i class="bi bi-check-circle text-success"></i>
                                <div>
                                    <h4>Sit rerum fuga</h4>
                                    <p>Quae dolorem earum veritatis oditseno</p>
                                    <p>2 hrs. ago</p>
                                </div>
                            </li>

                            <li>
                                <hr class="dropdown-divider">
                            </li>

                            <li class="notification-item">
                                <i class="bi bi-info-circle text-primary"></i>
                                <div>
                                    <h4>Dicta reprehenderit</h4>
                                    <p>Quae dolorem earum veritatis oditseno</p>
                                    <p>4 hrs. ago</p>
                                </div>
                            </li>

                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li class="dropdown-footer">
                                <a href="#">Show all notifications</a>
                            </li>

                        </ul><!-- End Notification Dropdown Items -->

                    </li><!-- End Notification Nav -->

                    <li class="nav-item dropdown">

                        <a class="nav-link nav-icon" asp-action="Index" asp-controller="settings" >
                            <i class="bi bi-gear"></i>
                        </a><!-- End Messages Icon -->



                    </li><!-- End Messages Nav -->

                    <li class="nav-item dropdown">

                        @* <partial name-="_SelectLanguage" /> *@



                    </li>

                </ul>
            </nav>

        </header>

        <aside id="sidebar" class="sidebar">

            <ul class="sidebar-nav " id="sidebar-nav">
                <li class="nav-item mb-5 text-center">
                    <!-- <a class="nav-link logoa"  href="index.html"> -->
                    <img class="mt-4" style="width: 200px;" src="/assets/img/124.jpg">
                    <!-- </a> -->
                </li><!-- End Dashboard Nav -->
                @{
                    var currentController = ViewContext.RouteData.Values["Controller"]?.ToString();
                    var currentAction = ViewContext.RouteData.Values["Action"]?.ToString();
                    var iconMargin = isRTL ? "margin-right: 10px;" : "margin-left: 10px;";
                    var flexDirection = isRTL ? "flex-direction: row-reverse;" : "";
                }

                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Home" && currentAction == "Index" ? "active" : "")"
                       asp-action="Index" asp-controller="Home" style="@flexDirection">
                        <i class="bi bi-grid" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">لوحة القيادة الرئيسية</span>
                    </a>
                </li><!-- End Dashboard Nav -->

                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Catfory" ? "active" : "")"
                       asp-action="Index" asp-controller="Catfory" style="@flexDirection">
                        <i class="bi bi-collection" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">الأقسام</span>
                    </a>
                </li>

                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Restaurant" ? "active" : "")"
                       asp-action="Index" asp-controller="Restaurant" style="@flexDirection">
                        <i class="bi bi-shop" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">إدارة المتاجر</span>
                    </a>
                </li>

               


                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Product" ? "active" : "")"
                       asp-action="Index" asp-controller="Product" style="@flexDirection">
                        <i class="bi bi-box-seam" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">إدارة المنتجات</span>
                    </a>
                </li><!-- End Dashboard Nav -->
                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Offer" ? "active" : "")"
                       asp-action="Index" asp-controller="Offer" style="@flexDirection">
                        <i class="bi bi-gift" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">ادارة الحزم</span>
                    </a>
                </li>


                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "User" ? "active" : "")"
                       asp-action="Index" asp-controller="User" style="@flexDirection">
                        <i class="bi bi-person-gear" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">إدارة المستخدمين</span>
                    </a>
                </li><!-- End Dashboard Nav -->

                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center" href="#" style="@flexDirection">
                        <i class="bi bi-bar-chart" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;"> التقارير المالية
                         <span class="badge bg-info">قريبا</span>
                        
                        </span>
                    </a>
                </li><!-- End Dashboard Nav -->
                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Ticket" ? "active" : "")"
                       asp-action="Index" asp-controller="Ticket" style="@flexDirection">
                        <i class="bi bi-headset" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;"> نظام الدعم الفني
                         
                        
                        </span>
                    </a>
                </li>
                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center @(currentController == "Content" ? "active" : "")"
                       asp-action="Index" asp-controller="Content" style="@flexDirection">
                        <i class="bi bi-file-text" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;"> إدارة المحتوى
                        
                        
                        </span>
                    </a>
                </li>
                <li class="nav-item ">
                    <a class="nav-link d-flex align-items-center" href="#" style="@flexDirection">
                        <i class="bi bi-globe" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;"> التحكم بالموقع الخارجي
                         <span class="badge bg-info">قريبا</span>
                        
                        </span>
                    </a>
                </li>
                <!-- End Dashboard Nav -->
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center mt-5" asp-action="Logout" asp-controller="Home" style="@flexDirection">
                        <i class="bi bi-box-arrow-right" style="@iconMargin font-size: 18px;"></i>
                        <span style="font-size: 16px; text-align: @(isRTL ? "right" : "left"); flex: 1;">تسجيل الخروج</span>
                    </a>
                </li><!-- End Login Page Nav -->



            </ul>

        </aside><!-- End Sidebar-->

    }
    <main id="main" class="main">

            @RenderBody()
        </main>

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="~/assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="~/assets/vendor/chart.js/chart.umd.js"></script>
    <script src="~/assets/vendor/quill/quill.js"></script>
    <script src="~/assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="~/assets/vendor/tinymce/tinymce.min.js"></script>
    @* <script src="https://code.jquery.com/jquery-1.11.3.min.js"></script> *@
    <!-- Template Main JS File -->
    <script src="~/assets/js/main.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
