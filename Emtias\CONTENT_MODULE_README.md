# قسم إدارة المحتوى - Content Module

## نظرة عامة
تم إنشاء قسم جديد لإدارة المحتوى في نظام Emtiaz، والذي يسمح بإدارة محتويات مثل:
- سياسة الخصوصية
- شروط الاستخدام
- الأسئلة الشائعة
- أي محتوى نصي آخر يحتاج إلى تنسيق احترافي

## الملفات التي تم إنشاؤها

### 1. Controller
- **المسار**: `Emtias/Controllers/ContentController.cs`
- **الوظائف**:
  - `Index()` - عرض قائمة جميع المحتويات
  - `Details(id)` - عرض تفاصيل محتوى معين
  - `Create()` - عرض نموذج إضافة محتوى جديد
  - `Create(content)` - حفظ محتوى جديد
  - `Edit(id)` - عرض نموذج تعديل محتوى
  - `Edit(id, content)` - حفظ التعديلات
  - `Delete(id)` - عرض صفحة تأكيد الحذف
  - `DeleteConfirmed(id)` - حذف المحتوى نهائياً

### 2. Views (الواجهات)

#### أ. Index.cshtml
- **المسار**: `Emtias/Views/Content/Index.cshtml`
- **الوظيفة**: عرض قائمة جميع المحتويات في جدول
- **المميزات**:
  - بحث في المحتويات
  - عرض تاريخ الإنشاء وآخر تحديث
  - أزرار للعرض والتعديل والحذف
  - رسائل نجاح/خطأ

#### ب. Create.cshtml
- **المسار**: `Emtias/Views/Content/Create.cshtml`
- **الوظيفة**: إضافة محتوى جديد
- **المميزات**:
  - حقل العنوان
  - محرر TinyMCE احترافي للمحتوى مع دعم RTL
  - التحقق من صحة البيانات
  - معاينة مباشرة

#### ج. Edit.cshtml
- **المسار**: `Emtias/Views/Content/Edit.cshtml`
- **الوظيفة**: تعديل محتوى موجود
- **المميزات**:
  - عرض معلومات الإنشاء والتحديث
  - محرر TinyMCE مع المحتوى الحالي
  - زر لاستعادة القيم الأصلية
  - التحقق من صحة البيانات

#### د. Details.cshtml
- **المسار**: `Emtias/Views/Content/Details.cshtml`
- **الوظيفة**: عرض تفاصيل المحتوى
- **المميزات**:
  - عرض العنوان والمحتوى المنسق
  - معلومات التواريخ
  - أزرار للتعديل والحذف
  - إمكانية الطباعة

#### هـ. Delete.cshtml
- **المسار**: `Emtias/Views/Content/Delete.cshtml`
- **الوظيفة**: تأكيد حذف المحتوى
- **المميزات**:
  - تحذير واضح
  - عرض معلومات المحتوى المراد حذفه
  - معاينة المحتوى
  - تأكيد مزدوج قبل الحذف

### 3. محرر TinyMCE

#### ملف اللغة العربية
- **المسار**: `Emtias/wwwroot/assets/vendor/tinymce/langs/ar.js`
- **الوظيفة**: ترجمة واجهة محرر TinyMCE إلى العربية
- **المحتوى**: ترجمة كاملة لجميع عناصر المحرر

#### إعدادات المحرر
المحرر مُعد بالخصائص التالية:
- **الارتفاع**: 600 بكسل
- **الاتجاه**: من اليمين لليسار (RTL)
- **اللغة**: العربية
- **الإضافات**:
  - advlist - قوائم متقدمة
  - autolink - روابط تلقائية
  - lists - قوائم
  - link - روابط
  - image - صور
  - charmap - خريطة الأحرف
  - preview - معاينة
  - anchor - مرساة
  - searchreplace - بحث واستبدال
  - visualblocks - كتل مرئية
  - code - كود
  - fullscreen - ملء الشاشة
  - insertdatetime - إدراج تاريخ/وقت
  - media - وسائط
  - table - جداول
  - help - مساعدة
  - wordcount - عدد الكلمات
  - directionality - الاتجاه

- **شريط الأدوات**:
  - تراجع/إعادة
  - كتل النص
  - تنسيق النص (عريض، مائل، تسطير، يتوسطه خط)
  - محاذاة النص
  - قوائم نقطية ومرقمة
  - ألوان النص والخلفية
  - روابط وصور ووسائط
  - جداول
  - تبديل الاتجاه (LTR/RTL)
  - إزالة التنسيق
  - كود المصدر
  - ملء الشاشة
  - معاينة
  - مساعدة

### 4. التحديثات على الملفات الموجودة

#### أ. HomeController.cs
- **التعديل**: إضافة عدد المحتويات إلى ViewBag
```csharp
ViewBag.TotalContents = await _context.Contents.CountAsync();
```

#### ب. Home/Index.cshtml
- **التعديل 1**: إضافة بطاقة إحصائيات المحتوى
  - عرض عدد المحتويات
  - رابط لإدارة المحتوى

- **التعديل 2**: إضافة زر إضافة محتوى جديد في قسم الإجراءات السريعة

## هيكل قاعدة البيانات

### جدول Content
الجدول موجود مسبقاً في قاعدة البيانات بالحقول التالية:
- `Id` (int) - المعرف الفريد
- `Title` (string) - العنوان
- `Content1` (string) - المحتوى (يدعم HTML)
- `CreatedAt` (DateTime) - تاريخ الإنشاء
- `UpdatedAt` (DateTime?) - تاريخ آخر تحديث (اختياري)

### DbContext
- الـ DbSet موجود مسبقاً: `public virtual DbSet<Content> Contents { get; set; }`

## الأسلوب البرمجي المتبع

تم اتباع نفس الأسلوب المستخدم في بقية الأقسام:

### 1. Controller
- استخدام async/await لجميع العمليات
- معالجة الأخطاء باستخدام try-catch
- التحقق من صحة البيانات
- رسائل TempData للنجاح والخطأ
- استخدام Bind لحماية من Over-posting

### 2. Views
- تصميم متجاوب باستخدام Bootstrap
- استخدام Bootstrap Icons
- رسائل تنبيه للمستخدم
- تنسيق موحد مع بقية الصفحات
- دعم RTL كامل

### 3. JavaScript
- التحقق من صحة النماذج
- معاينة مباشرة
- رسائل تأكيد للعمليات الحساسة
- إخفاء تلقائي للرسائل
- تأثيرات حركية

## كيفية الاستخدام

### 1. الوصول إلى القسم
- من الصفحة الرئيسية، انقر على "إدارة المحتوى" في بطاقة المحتويات
- أو انقر على "إضافة محتوى جديد" في قسم الإجراءات السريعة
- أو اذهب مباشرة إلى: `/Content/Index`

### 2. إضافة محتوى جديد
1. انقر على "إضافة محتوى جديد"
2. أدخل العنوان
3. استخدم المحرر لكتابة وتنسيق المحتوى
4. انقر على "حفظ المحتوى"

### 3. تعديل محتوى
1. من قائمة المحتويات، انقر على أيقونة التعديل
2. عدّل العنوان أو المحتوى
3. انقر على "حفظ التعديلات"

### 4. حذف محتوى
1. من قائمة المحتويات، انقر على أيقونة الحذف
2. راجع معلومات المحتوى
3. أكد الحذف

## المميزات الرئيسية

### 1. محرر احترافي
- محرر TinyMCE كامل المميزات
- دعم كامل للغة العربية
- دعم RTL
- إمكانية إضافة صور وروابط
- إمكانية إنشاء جداول
- تنسيق نصوص متقدم

### 2. واجهة سهلة الاستخدام
- تصميم نظيف ومنظم
- أزرار واضحة
- رسائل توضيحية
- تأكيدات للعمليات الحساسة

### 3. أمان
- التحقق من صحة البيانات
- حماية من Over-posting
- رسائل خطأ واضحة

### 4. أداء
- استخدام async/await
- استعلامات محسّنة
- تحميل سريع

## ملاحظات مهمة

1. **المحرر**: يستخدم TinyMCE 7.0.1 الموجود في المشروع
2. **اللغة**: تم إنشاء ملف ترجمة عربية كامل
3. **التوافق**: متوافق مع جميع المتصفحات الحديثة
4. **الأمان**: يتم حفظ HTML في قاعدة البيانات، تأكد من تنظيف المحتوى عند العرض للمستخدمين النهائيين
5. **الأسلوب**: تم اتباع نفس الأسلوب المستخدم في بقية المشروع بدقة

## الاختبار

تم بناء المشروع بنجاح بدون أخطاء:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

## التطوير المستقبلي (اختياري)

يمكن إضافة المميزات التالية مستقبلاً:
1. تصنيفات للمحتوى
2. البحث المتقدم
3. نسخ احتياطية للمحتوى
4. سجل التعديلات
5. صلاحيات الوصول
6. تصدير المحتوى
7. استيراد المحتوى

## الدعم الفني

في حالة وجود أي مشاكل:
1. تأكد من وجود ملف اللغة العربية في المسار الصحيح
2. تأكد من تحميل TinyMCE بشكل صحيح
3. راجع console المتصفح للأخطاء
4. تأكد من الاتصال بقاعدة البيانات

---

**تاريخ الإنشاء**: 2025-09-30
**الإصدار**: 1.0
**الحالة**: جاهز للاستخدام ✅

