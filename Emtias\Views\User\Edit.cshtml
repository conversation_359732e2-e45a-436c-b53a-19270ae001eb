@model Emtias.Models.AspNetUser

@{
    ViewData["Title"] = "تعديل المستخدم";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>تعديل المستخدم</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المستخدمين</a></li>
            <li class="breadcrumb-item active">تعديل</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">تعديل بيانات المستخدم: @Model.FullName</h5>

                    <form asp-action="Edit" class="row g-3 needs-validation" novalidate enctype="multipart/form-data">
                        <!-- Hidden Fields -->
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="PasswordHash" />
                        <input type="hidden" asp-for="SecurityStamp" />
                        <input type="hidden" asp-for="ConcurrencyStamp" />
                        <input type="hidden" asp-for="RefreshToken" />
                        <input type="hidden" asp-for="RefreshTokenExpiryTime" />
                        <input type="hidden" asp-for="NormalizedUserName" />
                        <input type="hidden" asp-for="NormalizedEmail" />
                        <input type="hidden" asp-for="EmailConfirmed" />
                        <input type="hidden" asp-for="PhoneNumberConfirmed" />
                        <input type="hidden" asp-for="TwoFactorEnabled" />
                        <input type="hidden" asp-for="LockoutEnabled" />
                        <input type="hidden" asp-for="AccessFailedCount" />
                        <input type="hidden" asp-for="NotificationToken" />

                        <!-- Model Errors Display -->
                        @if (!ViewData.ModelState.IsValid && ViewData.ModelState.ErrorCount > 0)
                        {
                            <div class="col-12">
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                    <ul class="mb-0 mt-2">
                                        @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                        {
                                            <li>@error.ErrorMessage</li>
                                        }
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            </div>
                        }

                        <!-- Current Image Display -->
                        <div class="col-12 text-center mb-4">
                            <div class="image-preview-container">
                                <div id="imagePreview" class="image-preview mx-auto mb-3" style="width: 120px; height: 120px; border: 2px dashed #dee2e6; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                    @if (!string.IsNullOrEmpty(Model.Image))
                                    {
                                        <img src="@Model.Image" alt="@Model.FullName" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" onerror="showDefaultImage(this)">
                                    }
                                    else
                                    {
                                        <i class="bi bi-person text-muted" style="font-size: 3rem;"></i>
                                    }
                                </div>
                                <p class="text-muted small">الصورة الحالية</p>
                            </div>
                        </div>

                        <!-- Image Upload Options -->
                        <div class="col-12 mb-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="imageFile" class="form-label">تغيير الصورة - رفع من الجهاز</label>
                                    <input type="file" id="imageFile" name="imageFile" class="form-control" accept="image/*" onchange="previewImage(this)">
                                    <div class="form-text">الحد الأقصى: 2MB، الأنواع المدعومة: JPG, PNG, GIF</div>
                                </div>
                                <div class="col-md-6">
                                    <label asp-for="Image" class="form-label">أو رابط الصورة</label>
                                    <input asp-for="Image" class="form-control" placeholder="https://example.com/image.jpg" onchange="previewImageUrl(this)">
                                    <span asp-validation-for="Image" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <label asp-for="FullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الكامل" required>
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="UserName" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input asp-for="UserName" class="form-control" placeholder="أدخل اسم المستخدم" required>
                            <span asp-validation-for="UserName" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="Email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input asp-for="Email" class="form-control" type="email" placeholder="<EMAIL>" required>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label for="password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" name="password" class="form-control" placeholder="اتركها فارغة إذا لم ترد تغييرها">
                            <div class="form-text">اتركها فارغة إذا لم ترد تغيير كلمة المرور</div>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="PhoneNumber" class="form-label">رقم الهاتف</label>
                            <input asp-for="PhoneNumber" class="form-control" placeholder="05xxxxxxxx">
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="IdentityNo" class="form-label">رقم الهوية</label>
                            <input asp-for="IdentityNo" class="form-control" placeholder="1xxxxxxxxx">
                            <span asp-validation-for="IdentityNo" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="BirthDate" class="form-label">تاريخ الميلاد</label>
                            <input asp-for="BirthDate" class="form-control" type="date">
                            <span asp-validation-for="BirthDate" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="Gender" class="form-label">الجنس</label>
                            <select asp-for="Gender" class="form-select">
                                <option value="">اختر الجنس</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                            <span asp-validation-for="Gender" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <label asp-for="UserType" class="form-label">نوع المستخدم</label>
                            <select asp-for="UserType" class="form-select">
                                <option value="1">مستخدم عادي</option>
                                <option value="2">مدير</option>
                                <option value="3">مشرف</option>
                            </select>
                            <span asp-validation-for="UserType" class="text-danger"></span>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check form-switch mt-4">
                                <input asp-for="Active" class="form-check-input" type="checkbox">
                                <label asp-for="Active" class="form-check-label">المستخدم نشط</label>
                            </div>
                        </div>

                    

                        <!-- Action Buttons -->
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-check-circle me-1"></i>
                                حفظ التغييرات
                            </button>
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                <i class="bi bi-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Preview image from file upload
    function previewImage(input) {
        const preview = document.getElementById('imagePreview');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">`;
                preview.style.border = '2px solid #198754';

                // Clear URL input
                document.querySelector('input[name="Image"]').value = '';
            };

            reader.readAsDataURL(input.files[0]);
        } else {
            resetImagePreview();
        }
    }

    // Preview image from URL
    function previewImageUrl(input) {
        const preview = document.getElementById('imagePreview');
        const url = input.value.trim();

        if (url) {
            const img = new Image();
            img.onload = function() {
                preview.innerHTML = `<img src="${url}" alt="Preview" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">`;
                preview.style.border = '2px solid #198754';

                // Clear file input
                document.getElementById('imageFile').value = '';
            };
            img.onerror = function() {
                preview.innerHTML = '<i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>';
                preview.style.border = '2px solid #dc3545';
            };
            img.src = url;
        } else {
            resetImagePreview();
        }
    }

    // Show default image on error
    function showDefaultImage(img) {
        img.style.display = 'none';
        const preview = document.getElementById('imagePreview');
        preview.innerHTML = '<i class="bi bi-person text-muted" style="font-size: 3rem;"></i>';
        preview.style.border = '2px dashed #dee2e6';
    }

    // Reset image preview
    function resetImagePreview() {
        const preview = document.getElementById('imagePreview');
        @if (!string.IsNullOrEmpty(Model.Image))
        {
            <text>
            preview.innerHTML = '<img src="@Model.Image" alt="@Model.FullName" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" onerror="showDefaultImage(this)">';
            </text>
        }
        else
        {
            <text>
            preview.innerHTML = '<i class="bi bi-person text-muted" style="font-size: 3rem;"></i>';
            </text>
        }
        preview.style.border = '2px dashed #dee2e6';
    }

    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>
