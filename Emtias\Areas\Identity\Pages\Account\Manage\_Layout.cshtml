﻿@{
    if (ViewData.TryGetValue("ParentLayout", out var parentLayout) && parentLayout !=  null)
    {
        Layout = parentLayout.ToString();
    }
    else
    {
        Layout = "/Areas/Identity/Pages/_Layout.cshtml";
    }
}

<h1>Manage your account</h1>

<div>
    <h2>Change your account settings</h2>
    <hr />
    <div class="row">
        <div class="col-md-3">
            <partial name="_ManageNav" />
        </div>
        <div class="col-md-9">
            @RenderBody()
        </div>
    </div>
</div>

@section Scripts {
    @RenderSection("Scripts", required: false)
}
