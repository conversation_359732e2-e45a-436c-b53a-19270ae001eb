@model Emtias.Models.Ticket

@{
    ViewData["Title"] = "إضافة تذكرة جديدة";
}

<div class="pagetitle">
    <h1>إضافة تذكرة جديدة</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="@Url.Action("Index")">إدارة التذاكر</a></li>
            <li class="breadcrumb-item active">إضافة تذكرة جديدة</li>
        </ol>
    </nav>
</div>

<!-- Error Messages -->
@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">معلومات التذكرة</h5>

                    <form asp-action="Create" method="post" id="ticketForm">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row mb-3">
                            <label asp-for="Title" class="col-sm-3 col-form-label">عنوان التذكرة <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input asp-for="Title" class="form-control" placeholder="أدخل عنوان التذكرة..." />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="UserId" class="col-sm-3 col-form-label">المستخدم <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select asp-for="UserId" class="form-select" asp-items="ViewBag.Users">
                                    <option value="">-- اختر المستخدم --</option>
                                </select>
                                <span asp-validation-for="UserId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Priority" class="col-sm-3 col-form-label">الأولوية <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select asp-for="Priority" class="form-select">
                                    <option value="">-- اختر الأولوية --</option>
                                    <option value="high">عالية</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="low">منخفضة</option>
                                </select>
                                <span asp-validation-for="Priority" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Description" class="col-sm-3 col-form-label">وصف التذكرة <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <textarea asp-for="Description" class="form-control" rows="6" placeholder="أدخل وصف مفصل للمشكلة أو الطلب..."></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">يرجى تقديم وصف مفصل ووضح للمساعدة في حل المشكلة بسرعة</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-9 offset-sm-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="termsCheck" required>
                                    <label class="form-check-label" for="termsCheck">
                                        أوافق على أن المعلومات المقدمة صحيحة وأن التذكرة ستتم معالجتها وفقاً لسياسة الدعم
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-check-circle"></i> إنشاء التذكرة
                                </button>
                                <button type="button" class="btn btn-secondary me-2" onclick="resetForm()">
                                    <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Help Card -->
<div class="row mt-4">
    <div class="col-lg-8 mx-auto">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> نصائح لإنشاء تذكرة فعالة</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>العنوان:</strong> اكتب عنواناً واضحاً ومختصراً يصف المشكلة</li>
                    <li><strong>الوصف:</strong> قدم تفاصيل كاملة عن المشكلة أو الطلب</li>
                    <li><strong>الأولوية:</strong> 
                        <ul>
                            <li><span class="badge bg-danger">عالية</span> - مشاكل تؤثر على العمل بشكل كبير</li>
                            <li><span class="badge bg-warning">متوسطة</span> - مشاكل مهمة لكن لا تمنع العمل</li>
                            <li><span class="badge bg-info">منخفضة</span> - طلبات تحسين أو مشاكل بسيطة</li>
                        </ul>
                    </li>
                    <li><strong>المعلومات المفيدة:</strong> أضف خطوات إعادة إنتاج المشكلة، رسائل الخطأ، لقطات الشاشة إن أمكن</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control:focus, .form-select:focus {
        border-color: #4154f1;
        box-shadow: 0 0 0 0.2rem rgba(65, 84, 241, 0.25);
    }
    
    .btn-primary {
        background-color: #4154f1;
        border-color: #4154f1;
    }
    
    .btn-primary:hover {
        background-color: #3749de;
        border-color: #3749de;
    }
    
    .card-header.bg-info {
        background-color: #0dcaf0 !important;
    }
    
    .text-danger {
        font-size: 0.875em;
    }
    
    .form-text {
        font-size: 0.875em;
        color: #6c757d;
    }
</style>

<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Form validation
    document.getElementById('ticketForm').addEventListener('submit', function(e) {
        const title = document.getElementById('Title').value.trim();
        const userId = document.getElementById('UserId').value;
        const priority = document.getElementById('Priority').value;
        const description = document.getElementById('Description').value.trim();
        const termsCheck = document.getElementById('termsCheck').checked;

        let isValid = true;
        let errorMessage = '';

        if (!title) {
            errorMessage += '• عنوان التذكرة مطلوب\n';
            isValid = false;
        }

        if (!userId) {
            errorMessage += '• يجب اختيار المستخدم\n';
            isValid = false;
        }

        if (!priority) {
            errorMessage += '• يجب تحديد الأولوية\n';
            isValid = false;
        }

        if (!description) {
            errorMessage += '• وصف التذكرة مطلوب\n';
            isValid = false;
        }

        if (!termsCheck) {
            errorMessage += '• يجب الموافقة على الشروط\n';
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();

            // Show error message in a Bootstrap alert
            showValidationErrors(errorMessage);
            return false;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإنشاء...';
        submitBtn.disabled = true;
    });

    // Reset form function
    function resetForm() {
        // Show confirmation modal instead of alert
        showResetConfirmation();
    }

    // Show validation errors in Bootstrap alert
    function showValidationErrors(errorMessage) {
        // Remove existing error alerts
        const existingAlerts = document.querySelectorAll('.validation-error-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show validation-error-alert';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <br><br>${errorMessage.replace(/\n/g, '<br>')}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert at the top of the form
        const form = document.getElementById('ticketForm');
        form.insertBefore(alertDiv, form.firstChild);

        // Scroll to top to show the error
        alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Show reset confirmation modal
    function showResetConfirmation() {
        // Remove existing modal if any
        const existingModal = document.getElementById('resetConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create confirmation modal
        const modalHtml = `
            <div class="modal fade" id="resetConfirmModal" tabindex="-1" aria-labelledby="resetConfirmModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="resetConfirmModalLabel">
                                <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                                تأكيد إعادة التعيين
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>هل أنت متأكد من إعادة تعيين النموذج؟</p>
                            <p class="text-muted">سيتم فقدان جميع البيانات المدخلة ولن يمكن استرجاعها.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="confirmReset()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('resetConfirmModal'));
        modal.show();
    }

    // Confirm reset action
    function confirmReset() {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('resetConfirmModal'));
        modal.hide();

        // Reset form
        document.getElementById('ticketForm').reset();

        // Clear validation errors
        const errorSpans = document.querySelectorAll('.text-danger');
        errorSpans.forEach(span => {
            if (span.getAttribute('data-valmsg-for')) {
                span.textContent = '';
            }
        });

        // Remove validation error alerts
        const existingAlerts = document.querySelectorAll('.validation-error-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Reset character counter
        updateCharacterCount();

        // Focus on first input
        document.getElementById('Title').focus();

        // Show success message
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show';
        successAlert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            تم إعادة تعيين النموذج بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const form = document.getElementById('ticketForm');
        form.insertBefore(successAlert, form.firstChild);

        // Auto dismiss after 3 seconds
        setTimeout(() => {
            if (successAlert.parentNode) {
                successAlert.remove();
            }
        }, 3000);
    }

    // Character counter for description
    const descriptionTextarea = document.getElementById('Description');
    const maxLength = 1000;
    
    // Create character counter element
    const counterElement = document.createElement('div');
    counterElement.className = 'form-text text-end';
    counterElement.id = 'descriptionCounter';
    descriptionTextarea.parentNode.appendChild(counterElement);
    
    function updateCounter() {
        const currentLength = descriptionTextarea.value.length;
        counterElement.textContent = `${currentLength} / ${maxLength} حرف`;
        
        if (currentLength > maxLength * 0.9) {
            counterElement.className = 'form-text text-end text-warning';
        } else if (currentLength > maxLength) {
            counterElement.className = 'form-text text-end text-danger';
        } else {
            counterElement.className = 'form-text text-end';
        }
    }
    
    descriptionTextarea.addEventListener('input', updateCounter);
    updateCounter(); // Initial call
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
