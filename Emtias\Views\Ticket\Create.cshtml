@model Emtias.Models.Ticket

@{
    ViewData["Title"] = "إضافة تذكرة جديدة";
}

<div class="pagetitle">
    <h1>إضافة تذكرة جديدة</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="@Url.Action("Index")">إدارة التذاكر</a></li>
            <li class="breadcrumb-item active">إضافة تذكرة جديدة</li>
        </ol>
    </nav>
</div>

<!-- Error Messages -->
@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @Html.Raw(TempData["ErrorMessage"])
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">معلومات التذكرة</h5>

                    <form asp-action="Create" method="post" id="ticketForm">
                        @Html.AntiForgeryToken()
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row mb-3">
                            <label asp-for="Title" class="col-sm-3 col-form-label">عنوان التذكرة <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input asp-for="Title" class="form-control" placeholder="أدخل عنوان التذكرة..." required />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="UserId" class="col-sm-3 col-form-label">المستخدم <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select asp-for="UserId" class="form-select" asp-items="ViewBag.Users" required>
                                    <option value="">-- اختر المستخدم --</option>
                                </select>
                                <span asp-validation-for="UserId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Priority" class="col-sm-3 col-form-label">الأولوية <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select asp-for="Priority" class="form-select" required>
                                    <option value="">-- اختر الأولوية --</option>
                                    <option value="high">عالية</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="low">منخفضة</option>
                                </select>
                                <span asp-validation-for="Priority" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label asp-for="Description" class="col-sm-3 col-form-label">وصف التذكرة <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <textarea asp-for="Description" class="form-control" rows="6" placeholder="أدخل وصف مفصل للمشكلة أو الطلب..." required></textarea>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">يرجى تقديم وصف مفصل ووضح للمساعدة في حل المشكلة بسرعة</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-9 offset-sm-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="termsCheck" required>
                                    <label class="form-check-label" for="termsCheck">
                                        أوافق على أن المعلومات المقدمة صحيحة وأن التذكرة ستتم معالجتها وفقاً لسياسة الدعم
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-check-circle"></i> إنشاء التذكرة
                                </button>
                              
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>



<style>
    .form-control:focus, .form-select:focus {
        border-color: #4154f1;
        box-shadow: 0 0 0 0.2rem rgba(65, 84, 241, 0.25);
    }
    
    .btn-primary {
        background-color: #4154f1;
        border-color: #4154f1;
    }
    
    .btn-primary:hover {
        background-color: #3749de;
        border-color: #3749de;
    }
    
    .card-header.bg-info {
        background-color: #0dcaf0 !important;
    }
    
    .text-danger {
        font-size: 0.875em;
    }
    
    .form-text {
        font-size: 0.875em;
        color: #6c757d;
    }
</style>

<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Form validation
    document.getElementById('ticketForm').addEventListener('submit', function(e) {
        const title = document.getElementById('Title').value.trim();
        const userId = document.getElementById('UserId').value;
        const priority = document.getElementById('Priority').value;
        const description = document.getElementById('Description').value.trim();
        const termsCheck = document.getElementById('termsCheck').checked;

        let isValid = true;
        let errorMessage = '';

        if (!title) {
            errorMessage += '• عنوان التذكرة مطلوب\n';
            isValid = false;
        }

        if (!userId) {
            errorMessage += '• يجب اختيار المستخدم\n';
            isValid = false;
        }

        if (!priority) {
            errorMessage += '• يجب تحديد الأولوية\n';
            isValid = false;
        }

        if (!description) {
            errorMessage += '• وصف التذكرة مطلوب\n';
            isValid = false;
        }

        if (!termsCheck) {
            errorMessage += '• يجب الموافقة على الشروط\n';
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();

            // Show error message in a Bootstrap alert
            showValidationErrors(errorMessage);
            return false;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإنشاء...';
        submitBtn.disabled = true;
    });

 

    // Show validation errors in Bootstrap alert
    function showValidationErrors(errorMessage) {
        // Remove existing error alerts
        const existingAlerts = document.querySelectorAll('.validation-error-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show validation-error-alert';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <br><br>${errorMessage.replace(/\n/g, '<br>')}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert at the top of the form
        const form = document.getElementById('ticketForm');
        form.insertBefore(alertDiv, form.firstChild);

        // Scroll to top to show the error
        alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

  
    // Confirm reset action
    function confirmReset() {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('resetConfirmModal'));
        modal.hide();

        // Reset form
        document.getElementById('ticketForm').reset();

        // Clear validation errors
        const errorSpans = document.querySelectorAll('.text-danger');
        errorSpans.forEach(span => {
            if (span.getAttribute('data-valmsg-for')) {
                span.textContent = '';
            }
        });

        // Remove validation error alerts
        const existingAlerts = document.querySelectorAll('.validation-error-alert');
        existingAlerts.forEach(alert => alert.remove());

        // Reset character counter
        updateCharacterCount();

        // Focus on first input
        document.getElementById('Title').focus();

        // Show success message
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show';
        successAlert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            تم إعادة تعيين النموذج بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const form = document.getElementById('ticketForm');
        form.insertBefore(successAlert, form.firstChild);

        // Auto dismiss after 3 seconds
        setTimeout(() => {
            if (successAlert.parentNode) {
                successAlert.remove();
            }
        }, 3000);
    }

    // Character counter for description
    const descriptionTextarea = document.getElementById('Description');
    const maxLength = 1000;
    
    // Create character counter element
    const counterElement = document.createElement('div');
    counterElement.className = 'form-text text-end';
    counterElement.id = 'descriptionCounter';
    descriptionTextarea.parentNode.appendChild(counterElement);
    
    function updateCounter() {
        const currentLength = descriptionTextarea.value.length;
        counterElement.textContent = `${currentLength} / ${maxLength} حرف`;
        
        if (currentLength > maxLength * 0.9) {
            counterElement.className = 'form-text text-end text-warning';
        } else if (currentLength > maxLength) {
            counterElement.className = 'form-text text-end text-danger';
        } else {
            counterElement.className = 'form-text text-end';
        }
    }
    
    descriptionTextarea.addEventListener('input', updateCounter);
    updateCounter(); // Initial call

    // Form validation enhancement
    const form = document.getElementById('ticketForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            let hasErrors = false;
            const errorMessages = [];

            // Check Title
            const titleInput = document.querySelector('input[name="Title"]');
            if (!titleInput.value.trim()) {
                errorMessages.push('• عنوان التذكرة مطلوب');
                titleInput.classList.add('is-invalid');
                hasErrors = true;
            } else {
                titleInput.classList.remove('is-invalid');
            }

            // Check UserId
            const userSelect = document.querySelector('select[name="UserId"]');
            if (!userSelect.value) {
                errorMessages.push('• يجب اختيار المستخدم');
                userSelect.classList.add('is-invalid');
                hasErrors = true;
            } else {
                userSelect.classList.remove('is-invalid');
            }

            // Check Priority
            const prioritySelect = document.querySelector('select[name="Priority"]');
            if (!prioritySelect.value) {
                errorMessages.push('• يجب اختيار الأولوية');
                prioritySelect.classList.add('is-invalid');
                hasErrors = true;
            } else {
                prioritySelect.classList.remove('is-invalid');
            }

            // Check Description
            const descriptionTextarea = document.querySelector('textarea[name="Description"]');
            if (!descriptionTextarea.value.trim()) {
                errorMessages.push('• وصف التذكرة مطلوب');
                descriptionTextarea.classList.add('is-invalid');
                hasErrors = true;
            } else {
                descriptionTextarea.classList.remove('is-invalid');
            }

            if (hasErrors) {
                e.preventDefault();
                showValidationErrors(errorMessages.join('\n'));
                return false;
            }
        });
    }
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
