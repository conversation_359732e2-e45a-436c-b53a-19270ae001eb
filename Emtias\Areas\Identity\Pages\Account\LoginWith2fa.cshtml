﻿@page
@model LoginWith2faModel
@{
    ViewData["Title"] = "Two-factor authentication";
}

<h1>@ViewData["Title"]</h1>
<hr />
<p>Your login is protected with an authenticator app. Enter your authenticator code below.</p>
<div class="row">
    <div class="col-md-4">
        <form method="post" asp-route-returnUrl="@Model.ReturnUrl">
            <input asp-for="RememberMe" type="hidden" />
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
            <div class="form-floating mb-3">
                <input asp-for="Input.TwoFactorCode" class="form-control" autocomplete="off" />
                <label asp-for="Input.TwoFactorCode" class="form-label"></label>
                <span asp-validation-for="Input.TwoFactorCode" class="text-danger"></span>
            </div>
            <div class="checkbox mb-3">
                <label asp-for="Input.RememberMachine" class="form-label">
                    <input asp-for="Input.RememberMachine" />
                    @Html.DisplayNameFor(m => m.Input.RememberMachine)
                </label>
            </div>
            <div>
                <button type="submit" class="w-100 btn btn-lg btn-primary">Log in</button>
            </div>
        </form>
    </div>
</div>
<p>
    Don't have access to your authenticator device? You can
    <a id="recovery-code-login" asp-page="./LoginWithRecoveryCode" asp-route-returnUrl="@Model.ReturnUrl">log in with a recovery code</a>.
</p>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}