/*html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Enhanced Category Management Styles */
/* ================================== */

/* Category Cards */
.category-card {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e3e6f0;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #fb7c35;
}

.category-icon, .category-icon-large {
    transition: transform 0.2s ease-in-out;
    border: 2px solid #f8f9fa;
}

.category-icon:hover, .category-icon-large:hover {
    transform: scale(1.05);
    border-color: #fb7c35;
}

.category-icon-placeholder, .category-icon-placeholder-large {
    transition: all 0.2s ease-in-out;
}

.category-icon-placeholder:hover, .category-icon-placeholder-large:hover {
    background-color: #e9ecef !important;
    border-color: #fb7c35 !important;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(251, 124, 53, 0.1);
    transform: scale(1.01);
    transition: all 0.2s ease-in-out;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Search and Filter Enhancements */
.search-form .form-control {
    border-radius: 25px;
    padding-left: 20px;
    border: 2px solid #e3e6f0;
    transition: all 0.3s ease-in-out;
}

.search-form .form-control:focus {
    border-color: #fb7c35;
    box-shadow: 0 0 0 0.2rem rgba(251, 124, 53, 0.25);
    transform: scale(1.02);
}

.btn-group .btn {
    transition: all 0.2s ease-in-out;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
}

/* Form Enhancements */
.needs-validation .form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38'/%3e%3c/svg%3e");
}

.needs-validation .form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
}

/* Icon Preview Enhancements */
.icon-preview, .icon-preview-container {
    transition: all 0.3s ease-in-out;
}

.icon-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Statistics Display */
.stats-icon {
    transition: all 0.3s ease-in-out;
}

.stats-icon:hover {
    transform: rotate(10deg) scale(1.1);
}

/* Alert Enhancements */
.alert {
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background:  #525c75 ;
    border: none;
}

.btn-primary:hover {
    background:  #fb7c35 ;
}


/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(251, 124, 53, 0.3);
    border-radius: 50%;
    border-top-color: #fb7c35;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .category-card {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-direction: column;
    }

  

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .category-icon-placeholder, .category-icon-placeholder-large {
        background-color: #343a40 !important;
        border-color: #495057 !important;
    }

    .form-control-plaintext {
        background-color: #343a40 !important;
        color: #fff !important;
    }
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}*/

.custom-label {
    font-size: 20pt;
    font-weight: 700;
}

/* Sidebar Active Menu Item Styles */
.sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, #fb7c35 0%, #e66a2b 100%) !important;
    color: #fff !important;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(251, 124, 53, 0.3);
    transform: translateX(-3px);
    border-right: 4px solid #fff;
}

.sidebar-nav .nav-link.active i {
    color: #fff !important;
    transform: scale(1.1);
}

.sidebar-nav .nav-link.active span {
    font-weight: 600;
}

/* RTL Support for Sidebar */
[dir="rtl"] .sidebar-nav .nav-link.active {
    transform: translateX(3px);
    border-right: none;
    border-left: 4px solid #fff !important;
}

/* Hover effect for non-active items */
.sidebar-nav .nav-link:not(.active):hover {
    background: rgba(251, 124, 53, 0.1) !important;
    color: #fb7c35 !important;
    transform: translateX(-2px);
    transition: all 0.3s ease-in-out;
}

[dir="rtl"] .sidebar-nav .nav-link:not(.active):hover {
    transform: translateX(2px);
}

.sidebar-nav .nav-link:not(.active):hover i {
    color: #fb7c35 !important;
    transform: scale(1.05);
}

/* RTL Fixes for Bootstrap Components */
/* ================================== */

/* Fix btn-group borders in RTL */
[dir="rtl"] .btn-group > .btn:not(:first-child),
[dir="rtl"] .btn-group > .btn-group:not(:first-child) {
    margin-right: -1px;
    margin-left: 0;
}

[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
[dir="rtl"] .btn-group > .btn-group:not(:last-child) > .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .btn-group > .btn:not(:first-child),
[dir="rtl"] .btn-group > .btn-group:not(:first-child) > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

[dir="rtl"] .btn-group > .btn:only-child {
    border-radius: 0.375rem;
}

/* Fix breadcrumb separators in RTL */
[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    float: right;
    padding-left: 0;
    padding-right: 0.5rem;
    color: #6c757d;
}

[dir="rtl"] .breadcrumb-item::before {
    content: none;
}

/* Fix dropdown menus in RTL */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

[dir="rtl"] .dropdown-menu-end {
    right: auto;
    left: 0;
}

/* Fix form controls in RTL */
[dir="rtl"] .form-control,
[dir="rtl"] .form-select {
    text-align: right;
}

[dir="rtl"] .input-group > .form-control:not(:last-child),
[dir="rtl"] .input-group > .form-select:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .input-group > .form-control:not(:first-child),
[dir="rtl"] .input-group > .form-select:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* Fix badges and pills in RTL */
[dir="rtl"] .badge {
    text-align: center;
}

/* Fix card components in RTL */
[dir="rtl"] .card-header {
    text-align: right;
}

[dir="rtl"] .card-body {
    text-align: right;
}

[dir="rtl"] .card-footer {
    text-align: right;
}

/* Fix table components in RTL */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .table th:first-child,
[dir="rtl"] .table td:first-child {
    border-right: none;
}

[dir="rtl"] .table th:last-child,
[dir="rtl"] .table td:last-child {
    border-left: none;
}

/* Fix pagination in RTL */
[dir="rtl"] .page-link {
    margin-right: -1px;
    margin-left: 0;
}

[dir="rtl"] .page-item:first-child .page-link {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="rtl"] .page-item:last-child .page-link {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Fix modal components in RTL */
[dir="rtl"] .modal-header {
    text-align: right;
}

[dir="rtl"] .modal-body {
    text-align: right;
}

[dir="rtl"] .modal-footer {
    text-align: right;
    justify-content: flex-start;
}

[dir="rtl"] .modal-footer > * {
    margin-left: 0;
    margin-right: 0.25rem;
}

/* Fix alert components in RTL */
[dir="rtl"] .alert {
    text-align: right;
}

[dir="rtl"] .alert-dismissible .btn-close {
    left: 0;
    right: auto;
}

/* Fix navbar components in RTL */
[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse;
}

[dir="rtl"] .navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0.5rem;
}

/* Fix list group in RTL */
[dir="rtl"] .list-group-item {
    text-align: right;
}

/* Fix progress bars in RTL */
[dir="rtl"] .progress-bar {
    right: 0;
    left: auto;
}

/* Fix tooltips and popovers in RTL */
[dir="rtl"] .tooltip,
[dir="rtl"] .popover {
    text-align: right;
}

/* Additional RTL fixes for specific components */
/* ============================================ */

/* Fix icon spacing in RTL */
[dir="rtl"] .bi {
    margin-left: 0;
    margin-right: 0.25rem;
}

/* Fix button icon spacing in RTL */
[dir="rtl"] .btn .bi {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* Fix search form in RTL */
[dir="rtl"] .search-form .form-control {
    padding-right: 20px;
    padding-left: 12px;
}

/* Fix input group in RTL */
[dir="rtl"] .input-group-text {
    border-left: 1px solid #ced4da;
    border-right: none;
}

/* Fix checkbox and radio in RTL */
[dir="rtl"] .form-check {
    text-align: right;
    padding-right: 1.25em;
    padding-left: 0;
}

[dir="rtl"] .form-check-input {
    float: right;
    margin-right: -1.25em;
    margin-left: 0;
}

/* Fix select dropdown arrow in RTL */
[dir="rtl"] .form-select {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 2.25rem;
}

/* Fix close button in RTL */
[dir="rtl"] .btn-close {
    float: left;
}

/* Fix offcanvas in RTL */
[dir="rtl"] .offcanvas-start {
    right: 0;
    left: auto;
    border-left: 1px solid rgba(0, 0, 0, 0.2);
    border-right: none;
    transform: translateX(100%);
}

[dir="rtl"] .offcanvas-end {
    left: 0;
    right: auto;
    border-right: 1px solid rgba(0, 0, 0, 0.2);
    border-left: none;
    transform: translateX(-100%);
}

/* Fix carousel controls in RTL */
[dir="rtl"] .carousel-control-prev {
    right: 0;
    left: auto;
}

[dir="rtl"] .carousel-control-next {
    left: 0;
    right: auto;
}

/* Fix accordion in RTL */
[dir="rtl"] .accordion-button {
    text-align: right;
}

[dir="rtl"] .accordion-button::after {
    margin-right: auto;
    margin-left: 0;
}

/* Fix tabs in RTL */
[dir="rtl"] .nav-tabs .nav-link {
    margin-left: -1px;
    margin-right: 0;
    border-top-right-radius: 0.375rem;
    border-top-left-radius: 0;
}

[dir="rtl"] .nav-tabs .nav-link:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0;
}

[dir="rtl"] .nav-tabs .nav-link:last-child {
    border-top-right-radius: 0.375rem;
    border-top-left-radius: 0;
}

/* Restaurant Details Page Styles */
/* =============================== */

/* Rating Display */
.rating-display .display-4 {
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rating-display .stars i {
    font-size: 1.5rem;
    margin: 0 2px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.rating-breakdown .progress {
    background-color: #f8f9fa;
    border-radius: 10px;
}

.rating-breakdown .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Comment Items */
.comment-item {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef !important;
}

.comment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    border-color: #fb7c35 !important;
}

/* Rating Items */
.rating-item {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef !important;
    background: linear-gradient(135deg, #fff9e6 0%, #fff 100%) !important;
}

.rating-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(251, 124, 53, 0.1) !important;
    border-color: #ffc107 !important;
}

.related-comment {
    background: linear-gradient(135deg, #fffbf0 0%, #fff 100%) !important;
    transition: all 0.2s ease;
}

.related-comment:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #fff 100%) !important;
}

.avatar-placeholder {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    transition: transform 0.2s ease;
}

.comment-item:hover .avatar-placeholder {
    transform: scale(1.05);
}

.rating-stars i {
    font-size: 1rem;
    margin: 0 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.comment-actions {
    padding-top: 8px;
    border-top: 1px solid #f8f9fa;
}

/* Offer Cards */
.card.shadow-sm {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef;
}

.card.shadow-sm:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    border-color: #fb7c35;
}

.card.shadow-sm .card-body {
    position: relative;
    overflow: hidden;
}

.card.shadow-sm .card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card.shadow-sm:hover .card-body::before {
    left: 100%;
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #fb7c35;
    border-color: #fb7c35;
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background-color: #fb7c35;
    border-color: #fb7c35;
    color: white;
    box-shadow: 0 4px 8px rgba(251, 124, 53, 0.3);
}

/* Statistics Cards Enhancement */
.info-card {
    transition: all 0.3s ease-in-out;
    border: 1px solid #e9ecef;
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    border-color: #fb7c35;
}

.info-card .card-icon {
    transition: transform 0.3s ease;
}

.info-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Badge Enhancements */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    border-radius: 6px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .rating-display .display-4 {
        font-size: 2.5rem;
    }

    .rating-display .stars i {
        font-size: 1.2rem;
    }

    .comment-item {
        margin-bottom: 1rem;
    }

    .card.shadow-sm:hover {
        transform: none;
    }

    .pagination .page-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}