# نظام إدارة التذاكر والدعم الفني - Tickets & Support System

## نظرة عامة
تم إنشاء نظام شامل لإدارة التذاكر والدعم الفني في نظام Emtiaz، والذي يتيح:
- إنشاء تذاكر الدعم من قبل المستخدمين
- تعيين التذاكر لفريق الدعم من قبل المدير الأعلى
- نظام دردشة مباشر بين المستخدم وفريق الدعم
- إدارة حالات وأولويات التذاكر
- إحصائيات شاملة لأداء الدعم

## الملفات التي تم إنشاؤها

### 1. Controllers

#### أ. TicketController.cs
- **المسار**: `Emtias/Controllers/TicketController.cs`
- **الوظائف الرئيسية**:
  - `Index()` - عرض قائمة التذاكر مع فلاتر (الحالة، الأولوية)
  - `Details(id)` - عرض تفاصيل التذكرة مع إمكانية التعيين وتغيير الحالة
  - `Create()` - إنشاء تذكرة جديدة
  - `Assign(id, assignedTo)` - تعيين التذكرة لمستخدم دعم
  - `UpdateStatus(id, status)` - تحديث حالة التذكرة (مفتوحة/مغلقة)
  - `UpdatePriority(id, priority)` - تحديث أولوية التذكرة

#### ب. TicketChatController.cs
- **المسار**: `Emtias/Controllers/TicketChatController.cs`
- **الوظائف الرئيسية**:
  - `Chat(id)` - عرض واجهة الدردشة للتذكرة
  - `SendMessage()` - إرسال رسالة في الدردشة
  - `SendMessageAjax()` - إرسال رسالة عبر AJAX
  - `GetMessages()` - جلب الرسائل الجديدة (AJAX)
  - `GetTicketInfo()` - جلب معلومات التذكرة (AJAX)

### 2. Views (الواجهات)

#### أ. Ticket/Index.cshtml
- **الوظيفة**: عرض قائمة جميع التذاكر مع إحصائيات
- **المميزات**:
  - بطاقات إحصائية (إجمالي، مفتوحة، مغلقة، غير معينة)
  - فلاتر حسب الحالة والأولوية
  - بحث في العناوين
  - جدول تفاعلي مع معلومات المستخدمين
  - أزرار للعرض والدردشة

#### ب. Ticket/Create.cshtml
- **الوظيفة**: إنشاء تذكرة جديدة
- **المميزات**:
  - نموذج شامل (العنوان، المستخدم، الأولوية، الوصف)
  - التحقق من صحة البيانات
  - عداد الأحرف للوصف
  - نصائح لإنشاء تذكرة فعالة
  - تأكيد الشروط

#### ج. Ticket/Details.cshtml
- **الوظيفة**: عرض تفاصيل التذكرة وإدارتها
- **المميزات**:
  - عرض شامل لمعلومات التذكرة
  - معلومات المستخدم وفريق الدعم
  - تعيين التذكرة لمستخدم دعم
  - تحديث الحالة والأولوية
  - معاينة آخر الرسائل
  - زر فتح الدردشة

#### د. TicketChat/Chat.cshtml
- **الوظيفة**: واجهة الدردشة المباشرة
- **المميزات**:
  - واجهة دردشة حديثة ومتجاوبة
  - رسائل مميزة للمستخدم وفريق الدعم
  - إرسال رسائل فوري عبر AJAX
  - تحديث تلقائي للرسائل الجديدة
  - معلومات المشاركين والإحصائيات
  - دعم Enter للإرسال و Shift+Enter للسطر الجديد

### 3. التحديثات على الملفات الموجودة

#### أ. HomeController.cs
- **التعديل**: إضافة إحصائيات التذاكر
```csharp
ViewBag.TotalTickets = await _context.Tickets.CountAsync();
ViewBag.OpenTickets = await _context.Tickets.Where(t => t.Status == true).CountAsync();
ViewBag.ClosedTickets = await _context.Tickets.Where(t => t.Status == false).CountAsync();
ViewBag.UnassignedTickets = await _context.Tickets.Where(t => t.AssignedTo == null).CountAsync();
ViewBag.HighPriorityTickets = await _context.Tickets.Where(t => t.Priority == "high").CountAsync();
```

#### ب. Home/Index.cshtml
- **التعديل 1**: إضافة بطاقات إحصائيات التذاكر (4 بطاقات)
  - إجمالي التذاكر
  - التذاكر المفتوحة
  - التذاكر المغلقة
  - التذاكر غير المعينة

- **التعديل 2**: إضافة أزرار الإجراءات السريعة
  - إضافة تذكرة جديدة
  - إدارة التذاكر

## هيكل قاعدة البيانات

### جدول Tickets
الجدول موجود مسبقاً بالحقول التالية:
- `Id` (int) - المعرف الفريد
- `Title` (string) - عنوان التذكرة
- `Description` (string) - وصف التذكرة
- `Status` (bool) - الحالة (true = مفتوحة، false = مغلقة)
- `Priority` (string) - الأولوية (high, medium, low)
- `UserId` (string) - معرف المستخدم صاحب التذكرة
- `AssignedTo` (string) - معرف مستخدم الدعم المعين
- `Vote` (int) - تقييم التذكرة
- `CreatedAt` (DateTime) - تاريخ الإنشاء
- `UpdatedAt` (DateTime?) - تاريخ آخر تحديث

### جدول TicketChats
الجدول موجود مسبقاً بالحقول التالية:
- `Id` (int) - المعرف الفريد
- `TicketId` (int) - معرف التذكرة
- `UserId` (string) - معرف المستخدم المرسل
- `Message` (string) - نص الرسالة
- `CreatedAt` (DateTime) - تاريخ الإرسال

### العلاقات
- `Ticket.User` - علاقة مع AspNetUser (صاحب التذكرة)
- `Ticket.AssignedToNavigation` - علاقة مع AspNetUser (المعين إليه)
- `Ticket.TicketChats` - علاقة مع TicketChat (رسائل التذكرة)
- `TicketChat.Ticket` - علاقة مع Ticket
- `TicketChat.User` - علاقة مع AspNetUser (مرسل الرسالة)

## سير العمل (Workflow)

### 1. إنشاء التذكرة
1. المستخدم ينشئ تذكرة جديدة
2. يحدد العنوان والوصف والأولوية
3. التذكرة تُنشأ بحالة "مفتوحة" و "غير معينة"

### 2. تعيين التذكرة
1. المدير الأعلى يدخل على تفاصيل التذكرة
2. يختار مستخدم من فريق الدعم
3. يتم تعيين التذكرة للمستخدم المحدد

### 3. بدء الدردشة
1. بعد التعيين، يظهر زر "فتح الدردشة"
2. مستخدم الدعم يدخل على الدردشة
3. يبدأ المحادثة مع صاحب التذكرة

### 4. حل المشكلة
1. يتم التواصل عبر الدردشة
2. مستخدم الدعم يحل المشكلة
3. يتم إغلاق التذكرة عند الانتهاء

## المميزات الرئيسية

### 1. نظام الأذونات
- **المستخدمون العاديون**: يمكنهم إنشاء تذاكر والمشاركة في الدردشة
- **فريق الدعم**: يمكنهم الرد على التذاكر المعينة إليهم
- **المدير الأعلى**: يمكنه تعيين التذاكر وإدارة جميع الجوانب

### 2. نظام الأولويات
- **عالية (High)**: مشاكل تؤثر على العمل بشكل كبير - لون أحمر
- **متوسطة (Medium)**: مشاكل مهمة لكن لا تمنع العمل - لون أصفر
- **منخفضة (Low)**: طلبات تحسين أو مشاكل بسيطة - لون أزرق

### 3. نظام الحالات
- **مفتوحة (Open)**: التذكرة نشطة ويمكن إضافة رسائل
- **مغلقة (Closed)**: التذكرة محلولة ولا يمكن إضافة رسائل

### 4. الدردشة المباشرة
- إرسال واستقبال فوري
- تحديث تلقائي كل 5 ثوانٍ
- واجهة مشابهة لتطبيقات الدردشة الحديثة
- دعم الأسطر المتعددة والتنسيق

### 5. الإحصائيات والتقارير
- إجمالي التذاكر
- التذاكر المفتوحة والمغلقة
- التذاكر غير المعينة
- التذاكر عالية الأولوية

## الأسلوب البرمجي المتبع

### 1. Controllers
- استخدام async/await لجميع العمليات
- معالجة شاملة للأخطاء مع Logging
- التحقق من الأذونات والصلاحيات
- رسائل TempData للتفاعل مع المستخدم
- دعم AJAX للعمليات المباشرة

### 2. Views
- تصميم متجاوب باستخدام Bootstrap
- استخدام Bootstrap Icons
- رسائل تنبيه تفاعلية
- تنسيق موحد مع بقية المشروع
- دعم RTL كامل
- JavaScript متقدم للتفاعل

### 3. قاعدة البيانات
- استخدام Entity Framework Core
- علاقات محددة بوضوح
- فهرسة مناسبة للاستعلامات
- تتبع التواريخ والتحديثات

## كيفية الاستخدام

### 1. إنشاء تذكرة جديدة
1. اذهب إلى الصفحة الرئيسية
2. انقر على "إضافة تذكرة جديدة"
3. املأ النموذج (العنوان، المستخدم، الأولوية، الوصف)
4. انقر على "إنشاء التذكرة"

### 2. إدارة التذاكر
1. اذهب إلى "إدارة التذاكر"
2. استخدم الفلاتر للبحث عن تذاكر محددة
3. انقر على "عرض التفاصيل" لأي تذكرة

### 3. تعيين التذكرة
1. في صفحة تفاصيل التذكرة
2. اختر مستخدم من قائمة "تعيين التذكرة"
3. انقر على "تعيين التذكرة"

### 4. بدء الدردشة
1. بعد تعيين التذكرة، انقر على "فتح الدردشة"
2. اكتب رسالتك في المربع السفلي
3. اضغط Enter للإرسال أو Shift+Enter للسطر الجديد

### 5. إدارة الحالة والأولوية
1. في صفحة تفاصيل التذكرة
2. استخدم أزرار "إدارة الحالة" لفتح/إغلاق التذكرة
3. استخدم قائمة "تحديث الأولوية" لتغيير الأولوية

## الأمان والحماية

### 1. التحقق من الأذونات
- التأكد من أن المستخدم مخول لإرسال رسائل في التذكرة
- التحقق من حالة المستخدم (نشط/غير نشط)
- حماية من الوصول غير المصرح به

### 2. التحقق من البيانات
- التحقق من صحة جميع المدخلات
- حماية من SQL Injection
- تنظيف البيانات قبل الحفظ

### 3. معالجة الأخطاء
- تسجيل شامل للأخطاء
- رسائل خطأ واضحة للمستخدم
- استرداد آمن من الأخطاء

## الأداء والتحسين

### 1. قاعدة البيانات
- استعلامات محسّنة مع Include للعلاقات
- فهرسة مناسبة للبحث والفلترة
- تحميل البيانات حسب الحاجة

### 2. الواجهة الأمامية
- تحديث AJAX للرسائل بدلاً من إعادة تحميل الصفحة
- تحميل تدريجي للرسائل القديمة
- تحسين الصور والأيقونات

### 3. الذاكرة
- تنظيف الموارد بعد الاستخدام
- إدارة فعالة للاتصالات
- تخزين مؤقت للبيانات المتكررة

## التطوير المستقبلي (اختياري)

### 1. مميزات إضافية
- إشعارات فورية (Real-time notifications)
- رفع الملفات في الدردشة
- تقييم جودة الدعم
- تقارير أداء مفصلة
- تصدير التذاكر والمحادثات

### 2. تحسينات تقنية
- SignalR للدردشة المباشرة
- تطبيق جوال للدعم
- API للتكامل مع أنظمة خارجية
- ذكاء اصطناعي لتصنيف التذاكر

### 3. إدارة متقدمة
- مستويات دعم متعددة
- تصعيد تلقائي للتذاكر
- قوالب الردود الجاهزة
- إحصائيات أداء الفريق

## الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من صحة اتصال قاعدة البيانات
2. راجع ملفات السجل (Logs) للأخطاء
3. تأكد من صحة أذونات المستخدمين
4. راجع console المتصفح للأخطاء JavaScript

### ملفات السجل المهمة:
- Application logs للأخطاء العامة
- Database logs لمشاكل قاعدة البيانات
- Browser console للأخطاء الأمامية

---

**تاريخ الإنشاء**: 2025-10-01
**الإصدار**: 1.0
**الحالة**: جاهز للاستخدام ✅

**المطور**: تم تطويره باستخدام ASP.NET Core MVC مع Entity Framework Core
**التوافق**: متوافق مع جميع المتصفحات الحديثة ومحسّن للأجهزة المحمولة
