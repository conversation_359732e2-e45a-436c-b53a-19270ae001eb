using Microsoft.AspNetCore.Http;

namespace Emtias.Services
{
   
    public interface IFileUploadService
    {
        /// رفع صورة إلى مجلد محدد مع تشفير الاسم
       
        Task<FileUploadResult> UploadImageAsync(IFormFile file, string folderName);

        
        /// حذف صورة من المجلد
     
        bool DeleteImage(string relativePath);

        
        /// التحقق من صحة الصورة
        
        FileValidationResult ValidateImageFile(IFormFile file);
    }

    
    /// نتيجة عملية رفع الملف
  
    public class FileUploadResult
    {
        public bool Success { get; set; }
        public string? RelativePath { get; set; }
        public string? ErrorMessage { get; set; }
        public string? OriginalFileName { get; set; }
        public string? EncodedFileName { get; set; }
        public long FileSize { get; set; }
    }


    /// نتيجة التحقق من صحة الملف
   
    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public string? FileExtension { get; set; }
        public long FileSize { get; set; }
    }
}
