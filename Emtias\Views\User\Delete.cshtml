@model Emtias.Models.AspNetUser

@{
    ViewData["Title"] = "حذف المستخدم";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>حذف المستخدم</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المستخدمين</a></li>
            <li class="breadcrumb-item active">حذف</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Alert -->
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>تحذير!</strong> هل أنت متأكد من رغبتك في حذف هذا المستخدم؟ سيتم إلغاء تفعيل الحساب وحظره من الوصول للنظام.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        تأكيد حذف المستخدم
                    </h5>

                    <!-- User Information Display -->
                    <div class="row mb-4">
                        <!-- User Image -->
                        <div class="col-md-4 text-center">
                            <div class="user-image-container mb-3">
                                @if (!string.IsNullOrEmpty(Model.Image))
                                {
                                    <img src="@Model.Image" alt="@Model.FullName" class="rounded-circle shadow" style="width: 120px; height: 120px; object-fit: cover;" onerror="showDefaultUserImage(this)">
                                }
                                else
                                {
                                    <div class="user-image-placeholder rounded-circle shadow d-flex align-items-center justify-content-center mx-auto" style="width: 120px; height: 120px; background-color: #e9ecef; border: 1px solid #dee2e6;">
                                        <i class="bi bi-person text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                }
                            </div>
                            <h6 class="text-muted">صورة المستخدم</h6>
                        </div>

                        <!-- User Details -->
                        <div class="col-md-8">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">الاسم الكامل:</label>
                                    <p class="">@Html.DisplayFor(model => model.FullName)</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">اسم المستخدم:</label>
                                    <p class="">@Html.DisplayFor(model => model.UserName)</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                    <p class="">@Html.DisplayFor(model => model.Email)</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">رقم الهاتف:</label>
                                    <p class="">@Html.DisplayFor(model => model.PhoneNumber)</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">نوع المستخدم:</label>
                                    <p class="">
                                        @switch (Model.UserType)
                                        {
                                            case 1:
                                                <span class="badge bg-info">مستخدم عادي</span>
                                                break;
                                            case 2:
                                                <span class="badge bg-warning">مدير</span>
                                                break;
                                            case 3:
                                                <span class="badge bg-success">مشرف</span>
                                                break;
                                            default:
                                                <span class="badge bg-secondary">غير محدد</span>
                                                break;
                                        }
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">الحالة:</label>
                                    <p class="">
                                        @if (Model.LockoutEnd.HasValue && Model.LockoutEnd > DateTimeOffset.Now)
                                        {
                                            <span class="badge bg-danger">محظور</span>
                                        }
                                        else if (Model.Active)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">غير نشط</span>
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Roles -->
                    @if (Model.Roles.Any())
                    {
                        <div class="row mb-4">
                            <div class="col-12">
                                <label class="form-label fw-bold">الأدوار:</label>
                                <div class="mt-2">
                                    @foreach (var role in Model.Roles)
                                    {
                                        <span class="badge bg-primary me-2 mb-1">@role.Name</span>
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">إحصائيات المستخدم:</h6>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="card-title text-primary mb-1">@ViewBag.OrdersCount</h6>
                                            <small class="text-muted">الطلبات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="card-title text-info mb-1">@ViewBag.ReservationsCount</h6>
                                            <small class="text-muted">الحجوزات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="card-title text-warning mb-1">@ViewBag.CommentsCount</h6>
                                            <small class="text-muted">التعليقات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="card-title text-success mb-1">@ViewBag.RatingsCount</h6>
                                            <small class="text-muted">التقييمات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center">
                        <form asp-action="Delete" method="post" class="d-inline">
                            <input type="hidden" asp-for="Id" />
                            <button type="submit" class="btn btn-danger me-2" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟ سيتم إلغاء تفعيل الحساب.')">
                                <i class="bi bi-trash me-1"></i>
                                تأكيد الحذف
                            </button>
                        </form>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                            <i class="bi bi-eye me-1"></i>
                            عرض التفاصيل
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                            <i class="bi bi-pencil me-1"></i>
                            تعديل
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Show default user image on error
    function showDefaultUserImage(img) {
        img.style.display = 'none';
        const placeholder = img.parentElement.querySelector('.user-image-placeholder') ||
                          img.parentElement.appendChild(document.createElement('div'));
        placeholder.className = 'user-image-placeholder rounded-circle shadow d-flex align-items-center justify-content-center mx-auto';
        placeholder.style.cssText = 'width: 120px; height: 120px; background-color: #e9ecef; border: 1px solid #dee2e6;';
        placeholder.innerHTML = '<i class="bi bi-person text-muted" style="font-size: 3rem;"></i>';
    }
</script>
