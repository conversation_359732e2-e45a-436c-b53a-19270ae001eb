using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Emtias.Models;
using Emtias.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;

namespace Emtias.Controllers;
// [Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly EmtiazDbContext _context;

    public HomeController(ILogger<HomeController> logger, EmtiazDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            // حساب التواريخ
            var today = DateTime.Now;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var last30Days = today.AddDays(-30);
            var last6Months = today.AddMonths(-6);

            // جمع الإحصائيات الأساسية
            var totalSales = await _context.Orders
                .Where(o => o.CreatedAt.HasValue)
                .SumAsync(o => o.TotalAmount);

            var monthlySales = await _context.Orders
                .Where(o => o.CreatedAt >= startOfMonth)
                .SumAsync(o => o.TotalAmount);

            // حساب إجمالي المستخدمين (من AspNetUsers)
            var totalUsers = await _context.AspNetUsers.CountAsync();

            // حساب المستخدمين النشطين هذا الشهر (من AspNetUsers)
            var newUsersThisMonth = await _context.AspNetUsers
                .Where(u => u.Active)
                .CountAsync();

            var soldPackages = await _context.OrderItems
                .Where(oi => oi.Order.CreatedAt.HasValue)
                .SumAsync(oi => oi.Quantity);

            var soldPackagesThisMonth = await _context.OrderItems
                .Where(oi => oi.Order.CreatedAt >= startOfMonth)
                .SumAsync(oi => oi.Quantity);

            // إحصائيات النمو للرسوم البيانية
            var userGrowthData = new List<object>();
            var salesGrowthData = new List<object>();

            for (int i = 5; i >= 0; i--)
            {
                var monthStart = today.AddMonths(-i).AddDays(1 - today.AddMonths(-i).Day);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthlyUsers = await _context.Employees
                    .Where(e => e.CreateAt >= monthStart && e.CreateAt <= monthEnd)
                    .CountAsync();

                var monthlySalesAmount = await _context.Orders
                    .Where(o => o.CreatedAt >= monthStart && o.CreatedAt <= monthEnd)
                    .SumAsync(o => o.TotalAmount);

                userGrowthData.Add(new {
                    month = monthStart.ToString("yyyy-MM"),
                    users = monthlyUsers
                });

                salesGrowthData.Add(new {
                    month = monthStart.ToString("yyyy-MM"),
                    sales = monthlySalesAmount
                });
            }

            var stats = new
            {
                // الإحصائيات الحالية
                TotalCategories = await _context.Catgories.CountAsync(),
                ActiveCategories = await _context.Catgories.CountAsync(),

                TotalRestaurants = await _context.Restaurants.CountAsync(),
                ActiveRestaurants = await _context.Restaurants.Where(r => !r.Deleted).CountAsync(),
                DeletedRestaurants = await _context.Restaurants.Where(r => r.Deleted).CountAsync(),

                TotalProducts = await _context.Products.CountAsync(),
                ActiveProducts = await _context.Products.Where(p => p.State == "active").CountAsync(),
                NewProducts = await _context.Products.Where(p => p.State == "new").CountAsync(),
                InactiveProducts = await _context.Products.Where(p => p.State == "inactive").CountAsync(),

                TotalOffers = await _context.Offers.CountAsync(),
                ActiveOffers = await _context.Offers.Where(o => o.State == "active").CountAsync(),
                ExpiredOffers = await _context.Offers.Where(o => o.EndDate < DateTime.Now).CountAsync(),
                CategoriesWithRestaurants = await _context.Catgories
                    .Where(c => c.Restaurants.Any())
                    .CountAsync(),

                // المؤشرات الجديدة
                TotalSales = totalSales,
                MonthlySales = monthlySales,
                TotalUsers = totalUsers,
                NewUsersThisMonth = newUsersThisMonth,
                SoldPackages = soldPackages,
                SoldPackagesThisMonth = soldPackagesThisMonth,

                // بيانات الرسوم البيانية
                UserGrowthData = userGrowthData,
                SalesGrowthData = salesGrowthData
            };

            ViewBag.Stats = stats;

            // إضافة عدد المحتويات
            ViewBag.TotalContents = await _context.Contents.CountAsync();

            // إحصائيات التذاكر
            ViewBag.TotalTickets = await _context.Tickets.CountAsync();
            ViewBag.OpenTickets = await _context.Tickets.Where(t => t.Status == true).CountAsync();
            ViewBag.ClosedTickets = await _context.Tickets.Where(t => t.Status == false).CountAsync();
            ViewBag.UnassignedTickets = await _context.Tickets.Where(t => t.AssignedTo == null).CountAsync();
            ViewBag.HighPriorityTickets = await _context.Tickets.Where(t => t.Priority == "high").CountAsync();

            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard statistics");
            // في حالة الخطأ، إرسال إحصائيات فارغة
            ViewBag.Stats = new
            {
                TotalCategories = 0,
                ActiveCategories = 0,
                TotalRestaurants = 0,
                ActiveRestaurants = 0,
                DeletedRestaurants = 0,
                TotalProducts = 0,
                ActiveProducts = 0,
                NewProducts = 0,
                InactiveProducts = 0,
                TotalOffers = 0,
                ActiveOffers = 0,
                ExpiredOffers = 0,
                CategoriesWithRestaurants = 0,
                TotalSales = 0m,
                MonthlySales = 0m,
                TotalUsers = 0,
                NewUsersThisMonth = 0,
                SoldPackages = 0,
                SoldPackagesThisMonth = 0,
                UserGrowthData = new List<object>(),
                SalesGrowthData = new List<object>()
            };
            return View();
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
