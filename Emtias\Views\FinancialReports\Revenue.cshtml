@{
    ViewData["Title"] = "تقرير الإيرادات";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="pagetitle">
    <h1>تقرير الإيرادات</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item">التقارير المالية</li>
            <li class="breadcrumb-item active">تقرير الإيرادات</li>
        </ol>
    </nav>
</div>

<!-- Success/Error Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-1"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <!-- Filters Card -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-funnel me-2"></i>
                        فلاتر التقرير
                    </h5>
                    
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" name="startDate" value="@ViewBag.StartDate">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" name="endDate" value="@ViewBag.EndDate">
                        </div>
                        
                        <div class="col-md-4">
                            <label for="restaurantId" class="form-label">المطعم (اختياري)</label>
                            <select class="form-select" id="restaurantId" name="restaurantId">
                                <option value="">جميع المطاعم</option>
                                @foreach (var restaurant in ViewBag.Restaurants)
                                {
                                    @if (ViewBag.SelectedRestaurantId != null && ViewBag.SelectedRestaurantId == restaurant.Id)
                                    {
                                        <option value="@restaurant.Id" selected>@restaurant.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@restaurant.Id">@restaurant.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search me-1"></i>
                                عرض التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="col-lg-4 col-md-6">
            <div class="card info-card revenue-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الإيرادات</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.TotalRevenue.ToString("N2") ريال</h6>
                            <span class="text-success small pt-1 fw-bold">عمولة المنصة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card info-card sales-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المبيعات</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-cart"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.TotalSales.ToString("N2") ريال</h6>
                            <span class="text-muted small pt-1 fw-bold">قيمة الطلبات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card info-card customers-card">
                <div class="card-body">
                    <h5 class="card-title">عدد الطلبات</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.TotalOrders</h6>
                            <span class="text-muted small pt-1 fw-bold">طلبية مكتملة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue by Restaurant -->
        @if (ViewBag.RevenueByRestaurant != null && ((IEnumerable<dynamic>)ViewBag.RevenueByRestaurant).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-shop me-2"></i>
                            الإيرادات حسب المطعم
                        </h5>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المطعم</th>
                                        <th>نسبة العمولة</th>
                                        <th>إجمالي المبيعات</th>
                                        <th>عمولة المنصة</th>
                                        <th>عدد الطلبات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in ViewBag.RevenueByRestaurant)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@item.RestaurantName</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@item.CommissionRate%</span>
                                            </td>
                                            <td>
                                                @item.TotalSales.ToString("N2") ريال
                                            </td>
                                            <td>
                                                <strong class="text-success">@item.TotalCommission.ToString("N2") ريال</strong>
                                            </td>
                                            <td>
                                                @item.OrderCount طلبية
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Revenue Chart -->
        @if (ViewBag.RevenueByDate != null && ((IEnumerable<dynamic>)ViewBag.RevenueByDate).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-graph-up me-2"></i>
                            الإيرادات اليومية
                        </h5>

                        <canvas id="revenueChart" style="max-height: 400px;"></canvas>
                    </div>
                </div>
            </div>
        }

        <!-- Detailed Revenue Table -->
        @if (ViewBag.RevenueDetails != null && ((IEnumerable<dynamic>)ViewBag.RevenueDetails).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-table me-2"></i>
                            تفاصيل العمولات المستحقة
                            <span class="badge bg-primary ms-2">@ViewBag.TotalRecords سجل</span>
                        </h5>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رقم الطلبية</th>
                                        <th>المطعم</th>
                                        <th>المنتج</th>
                                        <th>العرض</th>
                                        <th>التاريخ</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>إجمالي السعر</th>
                                        <th>نسبة العمولة</th>
                                        <th>العمولة المستحقة</th>
                                        <th>طريقة الدفع</th>
                                        <th>حالة الطلبية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in ViewBag.RevenueDetails)
                                    {
                                        <tr>
                                            <td><strong>#@item.OrderId</strong></td>
                                            <td>@item.RestaurantName</td>
                                            <td>@item.ProductName</td>
                                            <td>@item.OfferName</td>
                                            <td>@item.OrderDate?.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@item.Quantity</td>
                                            <td>@item.UnitPrice.ToString("N2") ريال</td>
                                            <td><strong>@item.TotalPrice.ToString("N2") ريال</strong></td>
                                            <td><span class="badge bg-info">@item.CommissionRate%</span></td>
                                            <td><strong class="text-success">@item.CommissionAmount.ToString("N2") ريال</strong></td>
                                            <td>
                                                @switch (item.PaymentType)
                                                {
                                                    case "cash":
                                                        <span class="badge bg-success">نقدي</span>
                                                        break;
                                                    case "card":
                                                        <span class="badge bg-primary">بطاقة</span>
                                                        break;
                                                    case "online":
                                                        <span class="badge bg-info">إلكتروني</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@item.PaymentType</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @switch (item.OrderStatus)
                                                {
                                                    case "completed":
                                                        <span class="badge bg-success">مكتمل</span>
                                                        break;
                                                    case "delivered":
                                                        <span class="badge bg-primary">تم التوصيل</span>
                                                        break;
                                                    case "paid":
                                                        <span class="badge bg-info">مدفوع</span>
                                                        break;
                                                    case "confirmed":
                                                        <span class="badge bg-warning">مؤكد</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@item.OrderStatus</span>
                                                        break;
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (ViewBag.TotalPages > 1)
                        {
                            <nav aria-label="صفحات التقرير" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="?startDate=@ViewBag.StartDate&endDate=@ViewBag.EndDate&restaurantId=@ViewBag.SelectedRestaurantId&page=@(ViewBag.CurrentPage - 1)">
                                                <i class="bi bi-chevron-right"></i> السابق
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == ViewBag.CurrentPage ? "active" : "")">
                                            <a class="page-link" href="?startDate=@ViewBag.StartDate&endDate=@ViewBag.EndDate&restaurantId=@ViewBag.SelectedRestaurantId&page=@i">@i</a>
                                        </li>
                                    }

                                    @if (ViewBag.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="?startDate=@ViewBag.StartDate&endDate=@ViewBag.EndDate&restaurantId=@ViewBag.SelectedRestaurantId&page=@(ViewBag.CurrentPage + 1)">
                                                التالي <i class="bi bi-chevron-left"></i>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>

                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    عرض @((ViewBag.CurrentPage - 1) * ViewBag.PageSize + 1) إلى @(Math.Min(ViewBag.CurrentPage * ViewBag.PageSize, ViewBag.TotalRecords)) من أصل @ViewBag.TotalRecords سجل
                                </small>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</section>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        @if (ViewBag.RevenueByDate != null && ((IEnumerable<dynamic>)ViewBag.RevenueByDate).Any())
        {
            <text>
            const revenueData = @Html.Raw(Json.Serialize(ViewBag.RevenueByDate));
            
            const ctx = document.getElementById('revenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: revenueData.map(item => new Date(item.date).toLocaleDateString('ar-SA')),
                    datasets: [{
                        label: 'الإيرادات (ريال)',
                        data: revenueData.map(item => item.totalRevenue),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'الإيرادات اليومية'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('ar-SA') + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
            </text>
        }
    </script>
}

<style>
    .info-card {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0px 0 30px rgba(1, 41, 112, 0.1);
        height: 100%;
        color: #444444;
    }

    .info-card h6 {
        font-size: 28px;
        color: #012970;
        font-weight: 700;
        margin: 0;
        padding: 0;
    }

    .card-icon {
        color: #012970;
        background: #f6f9ff;
        width: 64px;
        height: 64px;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .card-icon i {
        font-size: 28px;
    }

    .revenue-card .card-icon {
        color: #4154f1;
        background: #f6f6fe;
    }

    .sales-card .card-icon {
        color: #2eca6a;
        background: #e0f8e9;
    }

    .customers-card .card-icon {
        color: #ff771d;
        background: #ffecdf;
    }
</style>
