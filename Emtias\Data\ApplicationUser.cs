﻿using Microsoft.AspNetCore.Identity;

namespace Emtias.Data
{
    public class ApplicationUser : IdentityUser
    {
        public int UserType { get; set; }
        //public int Id { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? RefreshTokenExpiryTime { get; set; }
        public DateTime? BirthDate { get; set; }
        public string? FullName { get; set; }
        public string? IdentityNo { get; set; }
        public string? Image { get; set; }
        public string? Gender { get; set; }
        public bool Active { get; set; }
        public string? Status { get; set; }
        public string? NotificationToken { get; set; } // new by jalal
        public bool hasComplateData { get; set; }

        // Navigation properties for roles (not mapped to database)
        public List<string> UserRoles { get; set; } = new List<string>();
    }
}
