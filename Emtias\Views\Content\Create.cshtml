@model Emtias.Models.Content

@{
    ViewData["Title"] = "إضافة محتوى جديد";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>إضافة محتوى جديد</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المحتوى</a></li>
            <li class="breadcrumb-item active">إضافة جديد</li>
        </ol>
    </nav>
</div>

<!-- Main Content -->
<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">معلومات المحتوى الجديد</h5>

                    <form asp-action="Create" class="row g-3 needs-validation" novalidate>
                        <!-- Model Errors Display -->
                        @if (!ViewData.ModelState.IsValid && ViewData.ModelState.ErrorCount > 0)
                        {
                            <div class="col-12">
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                    <ul class="mb-0 mt-2">
                                        @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                        {
                                            <li>@error.ErrorMessage</li>
                                        }
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            </div>
                        }

                        <!-- Title -->
                        <div class="col-12">
                            <label asp-for="Title" class="form-label">العنوان <span class="text-danger">*</span></label>
                            <input asp-for="Title" class="form-control" placeholder="أدخل عنوان المحتوى (مثل: سياسة الخصوصية، شروط الاستخدام، الأسئلة الشائعة)" required />
                            <span asp-validation-for="Title" class="invalid-feedback"></span>
                            <div class="form-text">عنوان المحتوى كما سيظهر للمستخدمين</div>
                        </div>

                        <!-- Content Editor -->
                        <div class="col-12">
                            <label asp-for="Content1" class="form-label">المحتوى <span class="text-danger">*</span></label>
                            <textarea asp-for="Content1" class="tinymce-editor" required></textarea>
                            <span asp-validation-for="Content1" class="invalid-feedback"></span>
                            <div class="form-text">استخدم المحرر لتنسيق المحتوى بالشكل الذي تريد</div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-1"></i>
                                    العودة للقائمة
                                </a>
                                <div>
                                    <button type="reset" class="btn btn-outline-warning me-2" onclick="resetForm()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-1"></i>
                                        حفظ المحتوى
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <!-- TinyMCE Script -->
    <script src="~/assets/vendor/tinymce/tinymce.min.js"></script>

    <style>
        .alert-danger {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-danger ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }

        .alert-danger li {
            margin-bottom: 0.25rem;
        }

        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
            margin-top: 0.25rem;
        }

        .is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .tox-tinymce {
            border-radius: 0.375rem;
        }
    </style>

    <script>
        // Initialize TinyMCE Editor with RTL support
        tinymce.init({
            selector: 'textarea.tinymce-editor',
            height: 600,
            directionality: 'rtl',
            language: 'ar',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'directionality'
            ],
            toolbar: 'undo redo | blocks | bold italic underline strikethrough | ' +
                     'alignleft aligncenter alignright alignjustify | ' +
                     'bullist numlist outdent indent | forecolor backcolor | ' +
                     'link image media | table | ltr rtl | ' +
                     'removeformat | code fullscreen preview | help',
            menubar: 'file edit view insert format tools table help',
            content_style: 'body { font-family: Arial, sans-serif; font-size: 16px; direction: rtl; text-align: right; }',
            toolbar_mode: 'sliding',
            image_advtab: true,
            image_caption: true,
            quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote',
            contextmenu: 'link image table',
            promotion: false,
            branding: false,
            resize: true,
            statusbar: true,
            elementpath: true,
            setup: function(editor) {
                editor.on('init', function() {
                    console.log('TinyMCE Editor initialized successfully');
                });
            }
        });

        // Reset form function
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع البيانات المدخلة.')) {
                // Reset TinyMCE content
                tinymce.activeEditor.setContent('');
                
                // Reset form
                document.querySelector('form').reset();
                
                // Remove validation classes
                document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                document.querySelectorAll('.is-valid').forEach(el => el.classList.remove('is-valid'));
            }
            return false;
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.needs-validation');

            form.addEventListener('submit', function(event) {
                // Get TinyMCE content and set it to textarea
                const content = tinymce.activeEditor.getContent();
                document.querySelector('textarea[name="Content1"]').value = content;

                // Validate content
                if (!content || content.trim() === '') {
                    event.preventDefault();
                    event.stopPropagation();
                    alert('يرجى إدخال المحتوى');
                    return false;
                }

                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            });

            // Real-time validation for title
            const titleInput = document.querySelector('input[name="Title"]');
            if (titleInput) {
                titleInput.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                });
            }
        });

        // Auto-dismiss error alerts after 10 seconds
        setTimeout(function() {
            const errorAlerts = document.querySelectorAll('.alert-danger');
            errorAlerts.forEach(alert => {
                if (alert.querySelector('.btn-close')) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 10000);
    </script>
}

