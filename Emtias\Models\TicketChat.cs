﻿using System;
using System.Collections.Generic;

namespace Emtias.Models;

public partial class TicketChat
{
    public int Id { get; set; }

    public int TicketId { get; set; }

    public string UserId { get; set; } = null!;

    public string Message { get; set; } = null!;

    public DateTime CreatedAt { get; set; }

    public virtual Ticket Ticket { get; set; } = null!;

    public virtual AspNetUser User { get; set; } = null!;
}
