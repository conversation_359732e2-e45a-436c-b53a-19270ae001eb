using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;
using Microsoft.AspNetCore.Identity;
using Emtias.Data;
using Emtias.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;

namespace Emtias.Controllers
{
    // [Authorize]
    public class UserController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly IFileUploadService _fileUploadService;

        public UserController(EmtiazDbContext context, IFileUploadService fileUploadService)
        {
            _context = context;
            _fileUploadService = fileUploadService;
        }

        // GET: User
        public async Task<IActionResult> Index()
        {
            var users = await _context.AspNetUsers
                .Include(u => u.Roles)
                .OrderByDescending(u => u.Id)
                .ToListAsync();

            return View(users);
        }

        // GET: User/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.AspNetUsers
                .Include(u => u.Roles)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (user == null)
            {
                return NotFound();
            }

            // Get user statistics (AspNetUser doesn't directly link to Orders/Reservations)
            // These tables link to Employee table, not AspNetUser
            ViewBag.OrdersCount = 0; // Orders are linked to Employee table
            ViewBag.ReservationsCount = 0; // Reservations are linked to Employee table
            ViewBag.CommentsCount = 0; // Comments are linked to Employee table
            ViewBag.RatingsCount = 0; // Ratings are linked to Employee table
            ViewBag.TotalSpent = 0; // Orders are linked to Employee table

            return View(user);
        }

        // GET: User/Create
        public IActionResult Create()
        {
            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
            ViewBag.UserTypes = new SelectList(new[]
            {
                new { Value = 1, Text = "مستخدم عادي" },
                new { Value = 2, Text = "مدير" },
                new { Value = 3, Text = "مشرف" }
            }, "Value", "Text");

            return View();
        }

        // POST: User/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,UserName,Email,PhoneNumber,FullName,BirthDate,Gender,IdentityNo,Status,Active,UserType,HasComplateData,Image")] AspNetUser user, string password, IFormFile? imageFile = null)
        {
            // Set required fields before validation
            user.Id = Guid.NewGuid().ToString();
            user.EmailConfirmed = true;
            user.PhoneNumberConfirmed = true;
            user.SecurityStamp = Guid.NewGuid().ToString();
            user.ConcurrencyStamp = Guid.NewGuid().ToString();

    ModelState.Remove("Id");

            if (ModelState.IsValid)
            {
                try
                {
                    // Handle file upload if a file was provided
                    if (imageFile != null && imageFile.Length > 0)
                    {
                        var uploadResult = await _fileUploadService.UploadImageAsync(imageFile, "users");
                        if (uploadResult.Success)
                        {
                            user.Image = uploadResult.RelativePath;
                        }
                        else
                        {
                            ModelState.AddModelError("", uploadResult.ErrorMessage);
                            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
                            ViewBag.UserTypes = new SelectList(new[]
                            {
                                new { Value = 1, Text = "مستخدم عادي" },
                                new { Value = 2, Text = "مدير" },
                                new { Value = 3, Text = "مشرف" }
                            }, "Value", "Text");
                            return View(user);
                        }
                    }

                    // Hash password
                    var passwordHasher = new PasswordHasher<AspNetUser>();
                    user.PasswordHash = passwordHasher.HashPassword(user, password ?? "DefaultPassword123!");

                    _context.Add(user);
                    await _context.SaveChangesAsync();

                 

                    TempData["SuccessMessage"] = "تم إنشاء المستخدم بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء المستخدم: " + ex.Message;
                }
            }

            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
            ViewBag.UserTypes = new SelectList(new[]
            {
                new { Value = 1, Text = "مستخدم عادي" },
                new { Value = 2, Text = "مدير" },
                new { Value = 3, Text = "مشرف" }
            }, "Value", "Text");

            return View(user);
        }

        // GET: User/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.AspNetUsers
                .Include(u => u.Roles)
                .FirstOrDefaultAsync(u => u.Id == id);

            if (user == null)
            {
                return NotFound();
            }

            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
            ViewBag.UserTypes = new SelectList(new[]
            {
                new { Value = 1, Text = "مستخدم عادي" },
                new { Value = 2, Text = "مدير" },
                new { Value = 3, Text = "مشرف" }
            }, "Value", "Text", user.UserType);

            ViewBag.SelectedRoles = user.Roles.Select(r => r.Id).ToArray();

            return View(user);
        }

        // POST: User/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, [Bind("Id,UserName,Email,PhoneNumber,FullName,BirthDate,Gender,IdentityNo,Status,Active,UserType,HasComplateData,Image,EmailConfirmed,PhoneNumberConfirmed,TwoFactorEnabled,LockoutEnabled")] AspNetUser user, IFormFile? imageFile = null)
        {
            if (id != user.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Handle file upload if a file was provided
                    if (imageFile != null && imageFile.Length > 0)
                    {
                        // Get the old image path to delete it later
                        var oldUser = await _context.AspNetUsers.AsNoTracking().FirstOrDefaultAsync(u => u.Id == id);
                        var oldImagePath = oldUser?.Image;

                        var uploadResult = await _fileUploadService.UploadImageAsync(imageFile, "users");
                        if (uploadResult.Success)
                        {
                            user.Image = uploadResult.RelativePath;

                            // Delete old image file if it exists and is a local file
                            if (!string.IsNullOrEmpty(oldImagePath) && !oldImagePath.StartsWith("http"))
                            {
                                _fileUploadService.DeleteImage(oldImagePath);
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("", uploadResult.ErrorMessage);
                            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
                            ViewBag.UserTypes = new SelectList(new[]
                            {
                                new { Value = 1, Text = "مستخدم عادي" },
                                new { Value = 2, Text = "مدير" },
                                new { Value = 3, Text = "مشرف" }
                            }, "Value", "Text", user.UserType);
                            return View(user);
                        }
                    }

                    // Update AspNetUsers
                    _context.Update(user);
                    await _context.SaveChangesAsync();

                    // Update roles
                    var existingUser = await _context.AspNetUsers
                        .Include(u => u.Roles)
                        .FirstOrDefaultAsync(u => u.Id == id);

                    if (existingUser != null)
                    {
                        // Remove all existing roles
                        existingUser.Roles.Clear();

                

                        await _context.SaveChangesAsync();
                    }

                    TempData["SuccessMessage"] = "تم تحديث المستخدم بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!UserExists(user.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث المستخدم: " + ex.Message;
                }
            }

            ViewBag.Roles = new SelectList(_context.AspNetRoles, "Id", "Name");
            ViewBag.UserTypes = new SelectList(new[]
            {
                new { Value = 1, Text = "مستخدم عادي" },
                new { Value = 2, Text = "مدير" },
                new { Value = 3, Text = "مشرف" }
            }, "Value", "Text", user.UserType);


            return View(user);
        }

        // GET: User/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.AspNetUsers
                .Include(u => u.Roles)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (user == null)
            {
                return NotFound();
            }

            // Get user statistics for delete confirmation (AspNetUser doesn't directly link to these tables)
            ViewBag.OrdersCount = 0; // Orders are linked to Employee table
            ViewBag.ReservationsCount = 0; // Reservations are linked to Employee table
            ViewBag.CommentsCount = 0; // Comments are linked to Employee table
            ViewBag.RatingsCount = 0; // Ratings are linked to Employee table

            return View(user);
        }

        // POST: User/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            try
            {
                var user = await _context.AspNetUsers.FindAsync(id);
                if (user != null)
                {
                    // Delete associated image file if it exists and is a local file
                    if (!string.IsNullOrEmpty(user.Image) && !user.Image.StartsWith("http"))
                    {
                        _fileUploadService.DeleteImage(user.Image);
                    }

                    // Soft delete - just deactivate the user
                    user.Active = false;
                    user.LockoutEnabled = true;
                    user.LockoutEnd = DateTimeOffset.MaxValue;

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم إلغاء تفعيل المستخدم بنجاح";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المستخدم: " + ex.Message;
                return RedirectToAction(nameof(Delete), new { id = id });
            }
        }

        // Toggle user status
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(string id)
        {
            try
            {
                var user = await _context.AspNetUsers.FindAsync(id);
                if (user != null)
                {
                    user.Active = !user.Active;
                    if (user.Active)
                    {
                        user.LockoutEnd = null;
                    }
                    else
                    {
                        user.LockoutEnd = DateTimeOffset.MaxValue;
                    }

                    await _context.SaveChangesAsync();
                    return Json(new { success = true, newStatus = user.Active });
                }

                return Json(new { success = false, message = "المستخدم غير موجود" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        // Upload user image
        [HttpPost]
        public async Task<IActionResult> UploadImage(IFormFile file)
        {
            try
            {
                var uploadResult = await _fileUploadService.UploadImageAsync(file, "users");
                if (uploadResult.Success)
                {
                    return Json(new { success = true, filePath = uploadResult.RelativePath });
                }
                else
                {
                    return Json(new { success = false, message = uploadResult.ErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء رفع الصورة: " + ex.Message });
            }
        }

        private bool UserExists(string id)
        {
            return _context.AspNetUsers.Any(e => e.Id == id);
        }
    }
}
