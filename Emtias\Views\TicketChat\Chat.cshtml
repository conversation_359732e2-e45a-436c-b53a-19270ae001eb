@model Emtias.Models.Ticket

@{
    ViewData["Title"] = "دردشة التذكرة #" + Model.Id;
}

<div class="pagetitle">
    <h1>دردشة التذكرة #@Model.Id</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Ticket")">إدارة التذاكر</a></li>
            <li class="breadcrumb-item"><a href="@Url.Action("Details", "Ticket", new { id = Model.Id })">تفاصيل التذكرة #@Model.Id</a></li>
            <li class="breadcrumb-item active">الدردشة</li>
        </ol>
    </nav>
</div>

<!-- Success/Error Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-1"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <!-- Chat Area -->
        <div class="col-lg-8">
            <div class="card chat-card">
                <!-- Chat Header -->
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">@Model.Title</h6>
                            <small>
                                @if (Model.Status)
                                {
                                    <i class="bi bi-circle-fill text-success"></i> <span>مفتوحة</span>
                                }
                                else
                                {
                                    <i class="bi bi-circle-fill text-secondary"></i> <span>مغلقة</span>
                                }
                                | الأولوية: @Model.Priority
                            </small>
                        </div>
                        <div>
                            <a href="@Url.Action("Details", "Ticket", new { id = Model.Id })" class="btn btn-outline-light btn-sm">
                                <i class="bi bi-info-circle"></i> التفاصيل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div class="card-body p-0">
                    <div class="chat-messages" id="chatMessages">
                        @if (Model.TicketChats.Any())
                        {
                            @foreach (var message in Model.TicketChats)
                            {
                                <div class="message @(message.UserId == Model.UserId ? "message-user" : "message-support")" data-message-id="@message.Id">
                                    <div class="message-avatar">
                                        @if (!string.IsNullOrEmpty(message.User?.Image))
                                        {
                                            <img src="@message.User.Image" alt="صورة المستخدم" class="rounded-circle">
                                        }
                                        else
                                        {
                                            <i class="bi bi-person-circle"></i>
                                        }
                                    </div>
                                    <div class="message-content">
                                        <div class="message-header">
                                            <strong>@(message.User?.FullName ?? message.User?.UserName ?? "غير محدد")</strong>
                                            <small class="text-muted">@message.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                        </div>
                                        <div class="message-text">
                                            @Html.Raw(message.Message.Replace("\n", "<br>"))
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="no-messages">
                                <i class="bi bi-chat-dots"></i>
                                <p>لا توجد رسائل بعد. ابدأ المحادثة!</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Message Input -->
                @if (Model.Status)
                {
                    <div class="card-footer">
                        <form id="messageForm" class="d-flex">
                            @Html.AntiForgeryToken()
                            <input type="hidden" id="ticketId" value="@Model.Id" />
                            <input type="hidden" id="currentUserId" value="@ViewContext.HttpContext.User.Identity.Name" />
                            
                            <div class="flex-grow-1 me-2">
                                <textarea id="messageInput" class="form-control" rows="2" placeholder="اكتب رسالتك هنا..." required></textarea>
                            </div>
                            <div class="d-flex flex-column">
                                <button type="submit" class="btn btn-primary mb-1" id="sendButton">
                                    <i class="bi bi-send"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearMessage()">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                }
                else
                {
                    <div class="card-footer bg-light">
                        <div class="text-center text-muted">
                            <i class="bi bi-lock"></i>
                            التذكرة مغلقة. لا يمكن إضافة رسائل جديدة.
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Ticket Info -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">معلومات التذكرة</h6>
                    <div class="ticket-info">
                        <div class="info-row">
                            <strong>رقم التذكرة:</strong>
                            <span>#@Model.Id</span>
                        </div>
                        <div class="info-row">
                            <strong>الحالة:</strong>
                            @if (Model.Status)
                            {
                                <span class="badge bg-success">مفتوحة</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">مغلقة</span>
                            }
                        </div>
                        <div class="info-row">
                            <strong>الأولوية:</strong>
                            @switch (Model.Priority?.ToLower())
                            {
                                case "high":
                                    <span class="badge bg-danger">عالية</span>
                                    break;
                                case "medium":
                                    <span class="badge bg-warning">متوسطة</span>
                                    break;
                                case "low":
                                    <span class="badge bg-info">منخفضة</span>
                                    break;
                                default:
                                    <span class="badge bg-secondary">@Model.Priority</span>
                                    break;
                            }
                        </div>
                        <div class="info-row">
                            <strong>تاريخ الإنشاء:</strong>
                            <span>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div class="info-row">
                                <strong>آخر تحديث:</strong>
                                <span>@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Participants -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">المشاركون</h6>
                    
                    <!-- Ticket Owner -->
                    <div class="participant">
                        <div class="d-flex align-items-center mb-2">
                            @if (!string.IsNullOrEmpty(Model.User?.Image))
                            {
                                <img src="@Model.User.Image" alt="صورة المستخدم" class="rounded-circle me-2" style="width: 35px; height: 35px;">
                            }
                            else
                            {
                                <i class="bi bi-person-circle me-2" style="font-size: 35px; color: #007bff;"></i>
                            }
                            <div>
                                <strong>@(Model.User?.FullName ?? Model.User?.UserName ?? "غير محدد")</strong>
                                <br><small class="text-muted">صاحب التذكرة</small>
                            </div>
                        </div>
                    </div>

                    <!-- Assigned Support -->
                    @if (Model.AssignedToNavigation != null)
                    {
                        <div class="participant">
                            <div class="d-flex align-items-center">
                                @if (!string.IsNullOrEmpty(Model.AssignedToNavigation.Image))
                                {
                                    <img src="@Model.AssignedToNavigation.Image" alt="صورة الدعم" class="rounded-circle me-2" style="width: 35px; height: 35px;">
                                }
                                else
                                {
                                    <i class="bi bi-person-badge me-2" style="font-size: 35px; color: #28a745;"></i>
                                }
                                <div>
                                    <strong>@(Model.AssignedToNavigation.FullName ?? Model.AssignedToNavigation.UserName)</strong>
                                    <br><small class="text-muted">فريق الدعم</small>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Chat Statistics -->
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">إحصائيات الدردشة</h6>
                    <div class="stats">
                        <div class="stat-item">
                            <i class="bi bi-chat-dots text-primary"></i>
                            <span>إجمالي الرسائل: <strong>@Model.TicketChats.Count</strong></span>
                        </div>
                        @if (Model.TicketChats.Any())
                        {
                            <div class="stat-item">
                                <i class="bi bi-clock text-info"></i>
                                <span>آخر رسالة: <strong>@Model.TicketChats.OrderByDescending(tc => tc.CreatedAt).First().CreatedAt.ToString("dd/MM HH:mm")</strong></span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .chat-card {
        height: 70vh;
        display: flex;
        flex-direction: column;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f8f9fa;
    }
    
    .message {
        display: flex;
        margin-bottom: 20px;
        animation: fadeInUp 0.3s ease;
    }
    
    .message-user {
        flex-direction: row-reverse;
    }
    
    .message-user .message-content {
        background-color: #4154f1;
        color: white;
        margin-right: 10px;
    }
    
    .message-support .message-content {
        background-color: white;
        border: 1px solid #dee2e6;
        margin-left: 10px;
    }
    
    .message-avatar {
        flex-shrink: 0;
    }
    
    .message-avatar img {
        width: 40px;
        height: 40px;
    }
    
    .message-avatar i {
        font-size: 40px;
        color: #ccc;
    }
    
    .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
    }
    
    .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .message-header strong {
        font-size: 0.9em;
    }
    
    .message-header small {
        font-size: 0.8em;
        opacity: 0.8;
    }
    
    .message-text {
        line-height: 1.4;
    }
    
    .no-messages {
        text-align: center;
        padding: 50px 20px;
        color: #6c757d;
    }
    
    .no-messages i {
        font-size: 3rem;
        margin-bottom: 15px;
        display: block;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .participant {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .participant:last-child {
        border-bottom: none;
    }
    
    .stat-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .stat-item i {
        margin-right: 8px;
        width: 20px;
    }
    
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    #messageInput {
        resize: none;
        border-radius: 20px;
    }
    
    #messageInput:focus {
        border-color: #4154f1;
        box-shadow: 0 0 0 0.2rem rgba(65, 84, 241, 0.25);
    }
</style>

<script>
    let lastMessageTime = null;
    let isLoading = false;

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Scroll to bottom of messages
    function scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Initial scroll to bottom
    scrollToBottom();

    // Handle message form submission
    document.getElementById('messageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    // Handle Enter key in textarea (Shift+Enter for new line)
    document.getElementById('messageInput').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Send message function
    function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();
        
        if (!message) {
            return;
        }

        if (isLoading) {
            return;
        }

        isLoading = true;
        const sendButton = document.getElementById('sendButton');
        const originalButtonText = sendButton.innerHTML;
        sendButton.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        sendButton.disabled = true;

        const formData = new FormData();
        formData.append('ticketId', document.getElementById('ticketId').value);
        formData.append('message', message);
        formData.append('userId', '@ViewContext.HttpContext.User.Identity.Name'); // This should be replaced with actual user ID
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("SendMessageAjax", "TicketChat")', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageInput.value = '';
                addMessageToChat(data.data);
                scrollToBottom();
            } else {
                showErrorMessage('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('حدث خطأ أثناء إرسال الرسالة');
        })
        .finally(() => {
            isLoading = false;
            sendButton.innerHTML = originalButtonText;
            sendButton.disabled = false;
            messageInput.focus();
        });
    }

    // Add message to chat
    function addMessageToChat(messageData) {
        const chatMessages = document.getElementById('chatMessages');
        const noMessages = chatMessages.querySelector('.no-messages');
        
        if (noMessages) {
            noMessages.remove();
        }

        const messageDiv = document.createElement('div');
        const isCurrentUser = messageData.userId === '@Model.UserId';
        messageDiv.className = `message ${isCurrentUser ? 'message-user' : 'message-support'}`;
        messageDiv.setAttribute('data-message-id', messageData.id);

        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${messageData.userImage ? 
                    `<img src="${messageData.userImage}" alt="صورة المستخدم" class="rounded-circle">` :
                    `<i class="bi bi-person-circle"></i>`
                }
            </div>
            <div class="message-content">
                <div class="message-header">
                    <strong>${messageData.userName}</strong>
                    <small class="text-muted">${messageData.createdAtFormatted}</small>
                </div>
                <div class="message-text">
                    ${messageData.message.replace(/\n/g, '<br>')}
                </div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        lastMessageTime = messageData.createdAt;
    }

    // Clear message input
    function clearMessage() {
        const messageInput = document.getElementById('messageInput');
        if (messageInput.value.trim()) {
            showClearConfirmation();
        } else {
            messageInput.focus();
        }
    }

    // Show error message in toast
    function showErrorMessage(message) {
        // Remove existing error toasts
        const existingToasts = document.querySelectorAll('.error-toast');
        existingToasts.forEach(toast => toast.remove());

        // Create error toast
        const toastHtml = `
            <div class="toast error-toast align-items-center text-white bg-danger border-0 position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999;" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        // Add toast to page
        document.body.insertAdjacentHTML('beforeend', toastHtml);

        // Show toast
        const toastElement = document.querySelector('.error-toast');
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        // Remove from DOM after hiding
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    // Show clear confirmation modal
    function showClearConfirmation() {
        // Remove existing modal if any
        const existingModal = document.getElementById('clearConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create confirmation modal
        const modalHtml = `
            <div class="modal fade" id="clearConfirmModal" tabindex="-1" aria-labelledby="clearConfirmModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h6 class="modal-title" id="clearConfirmModalLabel">
                                <i class="bi bi-question-circle text-warning me-2"></i>
                                تأكيد المسح
                            </h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-2">هل أنت متأكد من مسح الرسالة؟</p>
                            <p class="text-muted small mb-0">لن يمكن استرجاع النص المكتوب.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="confirmClear()">
                                <i class="bi bi-trash me-1"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('clearConfirmModal'));
        modal.show();
    }

    // Confirm clear action
    function confirmClear() {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('clearConfirmModal'));
        modal.hide();

        // Clear message and focus
        const messageInput = document.getElementById('messageInput');
        messageInput.value = '';
        messageInput.focus();

        // Show success toast
        const successToastHtml = `
            <div class="toast success-toast align-items-center text-white bg-success border-0 position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999;" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-check-circle me-2"></i>
                        تم مسح الرسالة
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', successToastHtml);
        const successToast = new bootstrap.Toast(document.querySelector('.success-toast'), { delay: 2000 });
        successToast.show();

        // Remove from DOM after hiding
        document.querySelector('.success-toast').addEventListener('hidden.bs.toast', (e) => {
            e.target.remove();
        });
    }

    // Auto-refresh messages every 5 seconds
    @if (Model.Status)
    {
        <text>
        setInterval(function() {
            if (document.visibilityState === 'visible' && !isLoading) {
                checkForNewMessages();
            }
        }, 5000);
        </text>
    }

    // Check for new messages
    function checkForNewMessages() {
        const url = '@Url.Action("GetMessages", "TicketChat")' + 
                   '?ticketId=' + document.getElementById('ticketId').value +
                   (lastMessageTime ? '&lastMessageTime=' + encodeURIComponent(lastMessageTime) : '');

        fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.messages.length > 0) {
                data.messages.forEach(message => {
                    addMessageToChat(message);
                });
                scrollToBottom();
            }
        })
        .catch(error => {
            console.error('Error checking for new messages:', error);
        });
    }
</script>
