@{
    ViewData["Title"] = "تقرير مبيعات المطاعم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="pagetitle">
    <h1>تقرير مبيعات المطاعم</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item">التقارير المالية</li>
            <li class="breadcrumb-item active">تقرير مبيعات المطاعم</li>
        </ol>
    </nav>
</div>

<!-- Success/Error Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-1"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <!-- Filters Card -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-funnel me-2"></i>
                        فلاتر التقرير
                    </h5>
                    
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" name="startDate" value="@ViewBag.StartDate">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" name="endDate" value="@ViewBag.EndDate">
                        </div>
                        
                        <div class="col-md-4">
                            <label for="restaurantId" class="form-label">المطعم <span class="text-danger">*</span></label>
                            <select class="form-select" id="restaurantId" name="restaurantId" required>
                                <option value="">اختر المطعم</option>
                                @foreach (var restaurant in ViewBag.Restaurants)
                                {
                                    @if (ViewBag.SelectedRestaurantId != null && ViewBag.SelectedRestaurantId == restaurant.Id)
                                    {
                                        <option value="@restaurant.Id" selected>@restaurant.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@restaurant.Id">@restaurant.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search me-1"></i>
                                عرض التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        @if (ViewBag.Message != null)
        {
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    @ViewBag.Message
                </div>
            </div>
        }

        @if (ViewBag.Restaurant != null)
        {
            <!-- Restaurant Info -->
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-shop me-2"></i>
                            معلومات المطعم
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>اسم المطعم:</strong> @ViewBag.Restaurant.Name</p>
                                <p><strong>نسبة العمولة:</strong> <span class="badge bg-info">@ViewBag.Restaurant.ApplicationCommission%</span></p>
                            </div>
                            <div class="col-md-6">
                                @if (!string.IsNullOrEmpty(ViewBag.Restaurant.Address))
                                {
                                    <p><strong>العنوان:</strong> @ViewBag.Restaurant.Address</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="col-lg-3 col-md-6">
                <div class="card info-card sales-card">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي المبيعات</h5>
                        <div class="d-flex align-items-center">
                            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="ps-3">
                                <h6>@ViewBag.TotalSales.ToString("N2") ريال</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card info-card customers-card">
                    <div class="card-body">
                        <h5 class="card-title">عدد الطلبات</h5>
                        <div class="d-flex align-items-center">
                            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <div class="ps-3">
                                <h6>@ViewBag.TotalOrders</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card info-card revenue-card">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي الأصناف</h5>
                        <div class="d-flex align-items-center">
                            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                <i class="bi bi-box-seam"></i>
                            </div>
                            <div class="ps-3">
                                <h6>@ViewBag.TotalItems</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card info-card">
                    <div class="card-body">
                        <h5 class="card-title">متوسط قيمة الطلبية</h5>
                        <div class="d-flex align-items-center">
                            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                <i class="bi bi-calculator"></i>
                            </div>
                            <div class="ps-3">
                                <h6>@ViewBag.AverageOrderValue.ToString("N2") ريال</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales by Product -->
            @if (ViewBag.SalesByProduct != null && ((IEnumerable<dynamic>)ViewBag.SalesByProduct).Any())
            {
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-pie-chart me-2"></i>
                                المبيعات حسب المنتج
                            </h5>
                            
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية المباعة</th>
                                            <th>إجمالي المبيعات</th>
                                            <th>عدد الطلبات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in ViewBag.SalesByProduct)
                                        {
                                            <tr>
                                                <td><strong>@item.ProductName</strong></td>
                                                <td>@item.TotalQuantity</td>
                                                <td>@item.TotalSales.ToString("N2") ريال</td>
                                                <td>@item.OrderCount طلبية</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Sales Chart -->
            @if (ViewBag.SalesByDate != null && ((IEnumerable<dynamic>)ViewBag.SalesByDate).Any())
            {
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-graph-up me-2"></i>
                                المبيعات اليومية
                            </h5>
                            
                            <canvas id="salesChart" style="max-height: 400px;"></canvas>
                        </div>
                    </div>
                </div>
            }

            <!-- Detailed Sales Data -->
            @if (ViewBag.SalesData != null && ((IEnumerable<dynamic>)ViewBag.SalesData).Any())
            {
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-list-ul me-2"></i>
                                تفاصيل المبيعات
                            </h5>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلبية</th>
                                            <th>التاريخ</th>
                                            <th>المنتج</th>
                                            <th>العرض</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>الإجمالي</th>
                                            <th>طريقة الدفع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in ViewBag.SalesData)
                                        {
                                            <tr>
                                                <td>@item.OrderId</td>
                                                <td>@item.OrderDate?.ToString("dd/MM/yyyy HH:mm")</td>
                                                <td>@item.ProductName</td>
                                                <td>@item.OfferName</td>
                                                <td>@item.UnitPrice.ToString("N2")</td>
                                                <td>@item.Quantity</td>
                                                <td><strong>@item.TotalPrice.ToString("N2") ريال</strong></td>
                                                <td>
                                                    @switch (item.PaymentType)
                                                    {
                                                        case "cash":
                                                            <span class="badge bg-success">نقدي</span>
                                                            break;
                                                        case "card":
                                                            <span class="badge bg-primary">بطاقة</span>
                                                            break;
                                                        case "online":
                                                            <span class="badge bg-info">إلكتروني</span>
                                                            break;
                                                        default:
                                                            <span class="badge bg-secondary">@item.PaymentType</span>
                                                            break;
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</section>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Sales Chart
        @if (ViewBag.SalesByDate != null && ((IEnumerable<dynamic>)ViewBag.SalesByDate).Any())
        {
            <text>
            const salesData = @Html.Raw(Json.Serialize(ViewBag.SalesByDate));
            
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: salesData.map(item => new Date(item.date).toLocaleDateString('ar-SA')),
                    datasets: [{
                        label: 'المبيعات (ريال)',
                        data: salesData.map(item => item.totalSales),
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'المبيعات اليومية'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('ar-SA') + ' ريال';
                                }
                            }
                        }
                    }
                }
            });
            </text>
        }
    </script>
}

<style>
    .info-card {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0px 0 30px rgba(1, 41, 112, 0.1);
        height: 100%;
        color: #444444;
    }

    .info-card h6 {
        font-size: 28px;
        color: #012970;
        font-weight: 700;
        margin: 0;
        padding: 0;
    }

    .card-icon {
        color: #012970;
        background: #f6f9ff;
        width: 64px;
        height: 64px;
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .card-icon i {
        font-size: 28px;
    }

    .revenue-card .card-icon {
        color: #4154f1;
        background: #f6f6fe;
    }

    .sales-card .card-icon {
        color: #2eca6a;
        background: #e0f8e9;
    }

    .customers-card .card-icon {
        color: #ff771d;
        background: #ffecdf;
    }
</style>
