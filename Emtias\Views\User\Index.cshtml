@model IEnumerable<Emtias.Models.AspNetUser>

@{
    ViewData["Title"] = "إدارة المستخدمين";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>إدارة المستخدمين</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item active">المستخدمين</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success Message -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <!-- Error Message -->
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title">قائمة المستخدمين</h5>
                        <a class="btn btn-primary" asp-action="Create">
                            <i class="bi bi-plus-circle me-1"></i>
                            إضافة مستخدم جديد
                        </a>
                    </div>

                    <!-- Search and Filter Controls -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="search-form">
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في المستخدمين..." onkeyup="filterUsers()">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter" onchange="filterUsers()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="locked">محظور</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="typeFilter" onchange="filterUsers()">
                                <option value="">جميع الأنواع</option>
                                <option value="1">مستخدم عادي</option>
                                <option value="2">مدير</option>
                                <option value="3">مشرف</option>
                            </select>
                        </div>
                    </div>

                    <!-- Sorting Controls -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortUsers('name', 'asc')">
                                    <i class="bi bi-sort-alpha-down"></i> الاسم تصاعدي
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortUsers('name', 'desc')">
                                    <i class="bi bi-sort-alpha-up"></i> الاسم تنازلي
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortUsers('email', 'asc')">
                                    <i class="bi bi-sort-down"></i> البريد الإلكتروني
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="text-muted">
                                <span id="userCount">@Model.Count()</span> مستخدم
                            </span>
                        </div>
                    </div>

                    <!-- Table View -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الصورة</th>
                                    <th>الاسم الكامل</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>النوع</th>
                                    <th>الأدوار</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model)
                                {
                                    <tr data-user-id="@user.Id" data-name="@user.FullName" data-username="@user.UserName" data-email="@user.Email" data-type="@user.UserType" data-status="@(user.Active ? "active" : (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.Now ? "locked" : "inactive"))">
                                        <td>
                                            @if (!string.IsNullOrEmpty(user.Image))
                                            {
                                                <img src="@user.Image" alt="@user.FullName" class="user-image rounded-circle" style="width: 40px; height: 40px; object-fit: cover;" onerror="showDefaultUserImage(this)">
                                            }
                                            else
                                            {
                                                <div class="user-image-placeholder rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #e9ecef; border: 1px solid #dee2e6;">
                                                    <i class="bi bi-person text-muted"></i>
                                                </div>
                                            }
                                        </td>
                                        <td>
                                            <strong>@(user.FullName ?? "غير محدد")</strong>
                                        </td>
                                        <td>
                                            <span class="text-muted">@user.UserName</span>
                                        </td>
                                        <td>
                                            <span class="text-muted">@user.Email</span>
                                        </td>
                                        <td>
                                            <span class="text-muted">@(user.PhoneNumber ?? "غير محدد")</span>
                                        </td>
                                        <td>
                                            @switch (user.UserType)
                                            {
                                                case 1:
                                                    <span class="badge bg-info">مستخدم عادي</span>
                                                    break;
                                                case 2:
                                                    <span class="badge bg-warning">مدير</span>
                                                    break;
                                                case 3:
                                                    <span class="badge bg-success">مشرف</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-secondary">غير محدد</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (user.Roles.Any())
                                            {
                                                @foreach (var role in user.Roles.Take(2))
                                                {
                                                    <span class="badge bg-primary me-1">@role.Name</span>
                                                }
                                                @if (user.Roles.Count > 2)
                                                {
                                                    <span class="badge bg-secondary">+@(user.Roles.Count - 2)</span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا توجد أدوار</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.Now)
                                            {
                                                <span class="badge bg-danger">محظور</span>
                                            }
                                            else if (user.Active)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">غير نشط</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@user.Id" class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-outline-warning btn-sm" title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleUserStatus('@user.Id')" title="تغيير الحالة">
                                                    <i class="bi bi-toggle-@(user.Active ? "on" : "off")"></i>
                                                </button>
                                                <a asp-action="Delete" asp-route-id="@user.Id" class="btn btn-outline-danger btn-sm" title="حذف">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Filter users function
    function filterUsers() {
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const table = document.getElementById('usersTable');
        const rows = table.getElementsByTagName('tr');
        let visibleCount = 0;

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const name = row.getAttribute('data-name')?.toLowerCase() || '';
            const username = row.getAttribute('data-username')?.toLowerCase() || '';
            const email = row.getAttribute('data-email')?.toLowerCase() || '';
            const status = row.getAttribute('data-status') || '';
            const type = row.getAttribute('data-type') || '';

            const matchesSearch = name.includes(searchInput) ||
                                username.includes(searchInput) ||
                                email.includes(searchInput);
            const matchesStatus = !statusFilter || status === statusFilter;
            const matchesType = !typeFilter || type === typeFilter;

            if (matchesSearch && matchesStatus && matchesType) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        }

        document.getElementById('userCount').textContent = visibleCount;
    }

    // Sort users function
    function sortUsers(column, direction) {
        const table = document.getElementById('usersTable');
        const tbody = table.getElementsByTagName('tbody')[0];
        const rows = Array.from(tbody.getElementsByTagName('tr'));

        rows.sort((a, b) => {
            let aValue, bValue;

            switch(column) {
                case 'name':
                    aValue = a.getAttribute('data-name') || '';
                    bValue = b.getAttribute('data-name') || '';
                    break;
                case 'email':
                    aValue = a.getAttribute('data-email') || '';
                    bValue = b.getAttribute('data-email') || '';
                    break;
                default:
                    return 0;
            }

            if (direction === 'asc') {
                return aValue.localeCompare(bValue, 'ar');
            } else {
                return bValue.localeCompare(aValue, 'ar');
            }
        });

        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }

    // Toggle user status
    function toggleUserStatus(userId) {
        if (confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')) {
            fetch('/User/ToggleStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'id=' + encodeURIComponent(userId)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }
    }

    // Show default user image on error
    function showDefaultUserImage(img) {
        img.style.display = 'none';
        const placeholder = img.parentElement.querySelector('.user-image-placeholder') ||
                          img.parentElement.appendChild(document.createElement('div'));
        placeholder.className = 'user-image-placeholder rounded-circle d-flex align-items-center justify-content-center';
        placeholder.style.cssText = 'width: 40px; height: 40px; background-color: #e9ecef; border: 1px solid #dee2e6;';
        placeholder.innerHTML = '<i class="bi bi-person text-muted"></i>';
    }
</script>