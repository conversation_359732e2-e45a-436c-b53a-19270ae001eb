@model Emtias.Models.Ticket

@{
    ViewData["Title"] = "تفاصيل التذكرة #" + Model.Id;
}

<div class="pagetitle">
    <h1>تفاصيل التذكرة #@Model.Id</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="@Url.Action("Index")">إدارة التذاكر</a></li>
            <li class="breadcrumb-item active">تفاصيل التذكرة #@Model.Id</li>
        </ol>
    </nav>
</div>

<!-- Success/Error Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-1"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <!-- Ticket Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title">@Model.Title</h5>
                        <div class="ticket-actions">
                            @if (Model.AssignedTo != null)
                            {
                                <a href="@Url.Action("Chat", "TicketChat", new { id = Model.Id })" class="btn btn-success btn-sm me-2">
                                    <i class="bi bi-chat-dots"></i> فتح الدردشة
                                </a>
                            }
                            <a href="@Url.Action("Index")" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-arrow-left"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>

                    <!-- Status and Priority Badges -->
                    <div class="mb-3">
                        @if (Model.Status)
                        {
                            <span class="badge bg-success me-2">مفتوحة</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary me-2">مغلقة</span>
                        }

                        @switch (Model.Priority?.ToLower())
                        {
                            case "high":
                                <span class="badge bg-danger me-2">أولوية عالية</span>
                                break;
                            case "medium":
                                <span class="badge bg-warning me-2">أولوية متوسطة</span>
                                break;
                            case "low":
                                <span class="badge bg-info me-2">أولوية منخفضة</span>
                                break;
                            default:
                                <span class="badge bg-secondary me-2">@Model.Priority</span>
                                break;
                        }

                        <span class="badge bg-primary">@Model.TicketChats.Count رسالة</span>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                        <h6>وصف التذكرة:</h6>
                        <div class="ticket-description">
                            @Html.Raw(Model.Description?.Replace("\n", "<br>"))
                        </div>
                    </div>

                    <!-- Dates Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <i class="bi bi-calendar-plus text-primary"></i>
                                <strong>تاريخ الإنشاء:</strong>
                                <span>@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            @if (Model.UpdatedAt.HasValue)
                            {
                                <div class="info-item">
                                    <i class="bi bi-calendar-check text-success"></i>
                                    <strong>آخر تحديث:</strong>
                                    <span>@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">معلومات المستخدم</h6>
                    <div class="user-profile">
                        @if (!string.IsNullOrEmpty(Model.User?.Image))
                        {
                            <img src="@Model.User.Image" alt="صورة المستخدم" class="rounded-circle mb-2" style="width: 60px; height: 60px;">
                        }
                        else
                        {
                            <i class="bi bi-person-circle mb-2" style="font-size: 60px; color: #ccc;"></i>
                        }
                        <h6>@(Model.User?.FullName ?? Model.User?.UserName ?? "غير محدد")</h6>
                        @if (!string.IsNullOrEmpty(Model.User?.Email))
                        {
                            <p class="text-muted mb-0">@Model.User.Email</p>
                        }
                    </div>
                </div>
            </div>

            <!-- Assignment Section -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">تعيين التذكرة</h6>
                    
                    @if (Model.AssignedToNavigation != null)
                    {
                        <div class="assigned-user mb-3">
                            <div class="d-flex align-items-center">
                                @if (!string.IsNullOrEmpty(Model.AssignedToNavigation.Image))
                                {
                                    <img src="@Model.AssignedToNavigation.Image" alt="صورة المعين" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                                }
                                else
                                {
                                    <i class="bi bi-person-badge me-2" style="font-size: 40px; color: #28a745;"></i>
                                }
                                <div>
                                    <strong>@(Model.AssignedToNavigation.FullName ?? Model.AssignedToNavigation.UserName)</strong>
                                    @if (!string.IsNullOrEmpty(Model.AssignedToNavigation.Email))
                                    {
                                        <br><small class="text-muted">@Model.AssignedToNavigation.Email</small>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            التذكرة غير معينة لأي مستخدم
                        </div>
                    }

                    <!-- Assignment Form -->
                    <form asp-action="Assign" method="post" class="mb-3">
                        <input type="hidden" name="id" value="@Model.Id" />
                        <div class="mb-2">
                            <select name="assignedTo" class="form-select form-select-sm" asp-items="ViewBag.SupportUsers">
                                <option value="">-- اختر مستخدم الدعم --</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="bi bi-person-check"></i> تعيين التذكرة
                        </button>
                    </form>
                </div>
            </div>

            <!-- Status Management -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">إدارة الحالة</h6>
                    
                    <div class="d-grid gap-2">
                        @if (Model.Status)
                        {
                            <form asp-action="UpdateStatus" method="post" style="display: inline;">
                                <input type="hidden" name="id" value="@Model.Id" />
                                <input type="hidden" name="status" value="false" />
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="showStatusConfirmation(false, @Model.Id)">
                                    <i class="bi bi-x-circle"></i> إغلاق التذكرة
                                </button>
                            </form>
                        }
                        else
                        {
                            <form asp-action="UpdateStatus" method="post" style="display: inline;">
                                <input type="hidden" name="id" value="@Model.Id" />
                                <input type="hidden" name="status" value="true" />
                                <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="showStatusConfirmation(true, @Model.Id)">
                                    <i class="bi bi-check-circle"></i> إعادة فتح التذكرة
                                </button>
                            </form>
                        }
                    </div>
                </div>
            </div>

            <!-- Priority Management -->
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">تحديث الأولوية</h6>
                    
                    <form asp-action="UpdatePriority" method="post">
                        <input type="hidden" name="id" value="@Model.Id" />
                        <div class="mb-2">
                            <select name="priority" class="form-select form-select-sm">
                                <option value="high" selected="@(Model.Priority == "high" ? "selected" : null)">عالية</option>
                                <option value="medium" selected="@(Model.Priority == "medium" ? "selected" : null)">متوسطة</option>
                                <option value="low" selected="@(Model.Priority == "low" ? "selected" : null)">منخفضة</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-warning btn-sm w-100">
                            <i class="bi bi-flag"></i> تحديث الأولوية
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Messages Preview -->
    @if (Model.TicketChats.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="card-title mb-0">آخر الرسائل</h6>
                            @if (Model.AssignedTo != null)
                            {
                                <a href="@Url.Action("Chat", "TicketChat", new { id = Model.Id })" class="btn btn-primary btn-sm">
                                    <i class="bi bi-chat-dots"></i> عرض جميع الرسائل
                                </a>
                            }
                        </div>

                        <div class="messages-preview">
                            @foreach (var message in Model.TicketChats.OrderByDescending(tc => tc.CreatedAt).Take(3))
                            {
                                <div class="message-item">
                                    <div class="d-flex align-items-start">
                                        @if (!string.IsNullOrEmpty(message.User?.Image))
                                        {
                                            <img src="@message.User.Image" alt="صورة المستخدم" class="rounded-circle me-2" style="width: 35px; height: 35px;">
                                        }
                                        else
                                        {
                                            <i class="bi bi-person-circle me-2" style="font-size: 35px; color: #ccc;"></i>
                                        }
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <strong>@(message.User?.FullName ?? message.User?.UserName ?? "غير محدد")</strong>
                                                <small class="text-muted">@message.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                            </div>
                                            <p class="mb-0 mt-1">@message.Message</p>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        @if (Model.TicketChats.Count > 3)
                        {
                            <div class="text-center mt-3">
                                <small class="text-muted">و @(Model.TicketChats.Count - 3) رسائل أخرى...</small>
                            </div>
                        }

                        <!-- Action Buttons -->
                        <div class="text-center mt-3">
                            @if (Model.AssignedTo != null)
                            {
                                <a href="@Url.Action("Chat", "TicketChat", new { ticketId = Model.Id })" class="btn btn-primary btn-sm me-2">
                                    <i class="bi bi-chat-dots"></i> فتح الدردشة الكاملة
                                </a>
                            }
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleAllMessages()">
                                <i class="bi bi-eye"></i> <span id="toggleText">عرض جميع المراسلات</span>
                            </button>
                        </div>

                        <!-- All Messages (Hidden by default) -->
                        <div id="allMessages" style="display: none;" class="mt-3">
                            <hr>
                            <h6>جميع المراسلات (@Model.TicketChats.Count رسالة)</h6>
                            <div class="messages-all">
                                @foreach (var message in Model.TicketChats.OrderBy(tc => tc.CreatedAt))
                                {
                                    <div class="message-item-full">
                                        <div class="d-flex align-items-start">
                                            @if (!string.IsNullOrEmpty(message.User?.Image))
                                            {
                                                <img src="@message.User.Image" alt="صورة المستخدم" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                                            }
                                            else
                                            {
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                            }
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong>@(message.User?.FullName ?? message.User?.UserName ?? "غير محدد")</strong>
                                                    <small class="text-muted">@message.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                                </div>
                                                <div class="message-content mt-2">
                                                    @message.Message
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</section>

<style>
    .ticket-description {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        border-left: 4px solid #4154f1;
        line-height: 1.6;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .info-item i {
        margin-right: 8px;
        width: 20px;
    }
    
    .user-profile {
        text-align: center;
    }
    
    .assigned-user {
        background-color: #e8f5e8;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #d4edda;
    }
    
    .message-item {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .message-item:last-child {
        border-bottom: none;
    }
    
    .messages-preview {
        max-height: 300px;
        overflow-y: auto;
    }

    .message-item-full {
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .message-item-full:last-child {
        border-bottom: none;
    }

    .message-content {
        background-color: #f8f9fa;
        padding: 10px 15px;
        border-radius: 10px;
        border-left: 3px solid #4154f1;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .messages-all {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        background-color: #fafafa;
    }
    
    .ticket-actions {
        white-space: nowrap;
    }
</style>

<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Show status confirmation modal
    function showStatusConfirmation(newStatus, ticketId) {
        // Remove existing modal if any
        const existingModal = document.getElementById('statusConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        const isOpening = newStatus;
        const title = isOpening ? 'إعادة فتح التذكرة' : 'إغلاق التذكرة';
        const message = isOpening ?
            'هل أنت متأكد من إعادة فتح التذكرة؟' :
            'هل أنت متأكد من إغلاق التذكرة؟ لن يتمكن المستخدم من إضافة رسائل جديدة.';
        const buttonClass = isOpening ? 'btn-success' : 'btn-secondary';
        const buttonIcon = isOpening ? 'bi-check-circle' : 'bi-x-circle';

        // Create confirmation modal
        const modalHtml = `
            <div class="modal fade" id="statusConfirmModal" tabindex="-1" aria-labelledby="statusConfirmModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="statusConfirmModalLabel">
                                <i class="bi bi-question-circle text-warning me-2"></i>
                                تأكيد ${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                            ${!isOpening ? '<p class="text-muted small">سيتم منع إضافة رسائل جديدة في الدردشة.</p>' : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn ${buttonClass}" onclick="confirmStatusChange(${newStatus}, ${ticketId})">
                                <i class="${buttonIcon} me-1"></i>
                                ${title}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('statusConfirmModal'));
        modal.show();
    }

    // Confirm status change
    function confirmStatusChange(newStatus, ticketId) {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusConfirmModal'));
        modal.hide();

        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '@Url.Action("UpdateStatus", "Ticket")';

        // Add CSRF token
        const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '__RequestVerificationToken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Add ticket ID
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = ticketId;
        form.appendChild(idInput);

        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        form.appendChild(statusInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }

    // Toggle all messages visibility
    function toggleAllMessages() {
        const allMessages = document.getElementById('allMessages');
        const toggleText = document.getElementById('toggleText');

        if (allMessages.style.display === 'none') {
            allMessages.style.display = 'block';
            toggleText.textContent = 'إخفاء المراسلات';
        } else {
            allMessages.style.display = 'none';
            toggleText.textContent = 'عرض جميع المراسلات';
        }
    }

    // Auto-refresh messages preview every 30 seconds if ticket is open
    @if (Model.Status && Model.AssignedTo != null)
    {
        <text>
        setInterval(function() {
            // Simple refresh of the page to get latest messages
            // In a real application, you might want to use AJAX to update just the messages section
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000); // 30 seconds
        </text>
    }
</script>
