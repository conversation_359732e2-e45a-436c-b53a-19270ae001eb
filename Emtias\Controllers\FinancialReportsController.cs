using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;
using Microsoft.AspNetCore.Authorization;

namespace Emtias.Controllers
{
    // [Authorize]
    public class FinancialReportsController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly ILogger<FinancialReportsController> _logger;

        public FinancialReportsController(EmtiazDbContext context, ILogger<FinancialReportsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: FinancialReports/Revenue
        public async Task<IActionResult> Revenue(DateTime? startDate, DateTime? endDate, int? restaurantId)
        {
            try
            {
                // Set default date range if not provided (last 30 days)
                var defaultEndDate = DateTime.Now.Date;
                var defaultStartDate = defaultEndDate.AddDays(-30);

                startDate ??= defaultStartDate;
                endDate ??= defaultEndDate;

                // Ensure end date includes the full day
                endDate = endDate.Value.Date.AddDays(1).AddTicks(-1);

                // Get revenue data
                var revenueQuery = from oi in _context.OrderItems
                                   join o in _context.Orders on oi.OrderId equals o.OrderId
                                   join r in _context.Restaurants on oi.RestId equals r.Id
                                   where o.CreatedAt >= startDate && o.CreatedAt <= endDate
                                   && o.Status == "completed" // Only completed orders
                                   select new
                                   {
                                       OrderId = o.OrderId,
                                       RestaurantId = r.Id,
                                       RestaurantName = r.Name,
                                       OrderDate = o.CreatedAt,
                                       TotalPrice = oi.TotalPrice,
                                       CommissionRate = r.ApplicationCommission,
                                       CommissionAmount = (oi.TotalPrice * (decimal)(r.ApplicationCommission / 100))
                                   };

                // Filter by restaurant if specified
                if (restaurantId.HasValue)
                {
                    revenueQuery = revenueQuery.Where(x => x.RestaurantId == restaurantId.Value);
                }

                var revenueData = await revenueQuery.ToListAsync();

                // Calculate totals
                var totalRevenue = revenueData.Sum(x => x.CommissionAmount);
                var totalOrders = revenueData.Select(x => x.OrderId).Distinct().Count();
                var totalSales = revenueData.Sum(x => x.TotalPrice);

                // Group by restaurant
                var revenueByRestaurant = revenueData
                    .GroupBy(x => new { x.RestaurantId, x.RestaurantName, x.CommissionRate })
                    .Select(g => new
                    {
                        RestaurantId = g.Key.RestaurantId,
                        RestaurantName = g.Key.RestaurantName,
                        CommissionRate = g.Key.CommissionRate,
                        TotalSales = g.Sum(x => x.TotalPrice),
                        TotalCommission = g.Sum(x => x.CommissionAmount),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderByDescending(x => x.TotalCommission)
                    .ToList();

                // Group by date for chart
                var revenueByDate = revenueData
                    .GroupBy(x => x.OrderDate.Value.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalRevenue = g.Sum(x => x.CommissionAmount),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderBy(x => x.Date)
                    .ToList();

                // Get restaurants for dropdown
                var restaurants = await _context.Restaurants
                    .Where(r => !r.Deleted)
                    .Select(r => new { r.Id, r.Name })
                    .OrderBy(r => r.Name)
                    .ToListAsync();

                ViewBag.Restaurants = restaurants;
                ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
                ViewBag.EndDate = endDate.Value.Date.ToString("yyyy-MM-dd");
                ViewBag.SelectedRestaurantId = restaurantId;
                ViewBag.TotalRevenue = totalRevenue;
                ViewBag.TotalOrders = totalOrders;
                ViewBag.TotalSales = totalSales;
                ViewBag.RevenueByRestaurant = revenueByRestaurant;
                ViewBag.RevenueByDate = revenueByDate;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating revenue report");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء تقرير الإيرادات";
                return RedirectToAction("Index", "Home");
            }
        }

        // GET: FinancialReports/RestaurantSales
        public async Task<IActionResult> RestaurantSales(int? restaurantId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // Set default date range if not provided (last 30 days)
                var defaultEndDate = DateTime.Now.Date;
                var defaultStartDate = defaultEndDate.AddDays(-30);

                startDate ??= defaultStartDate;
                endDate ??= defaultEndDate;

                // Ensure end date includes the full day
                endDate = endDate.Value.Date.AddDays(1).AddTicks(-1);

                // Get restaurants for dropdown
                var restaurants = await _context.Restaurants
                    .Where(r => !r.Deleted)
                    .Select(r => new { r.Id, r.Name })
                    .OrderBy(r => r.Name)
                    .ToListAsync();

                ViewBag.Restaurants = restaurants;
                ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
                ViewBag.EndDate = endDate.Value.Date.ToString("yyyy-MM-dd");
                ViewBag.SelectedRestaurantId = restaurantId;

                if (!restaurantId.HasValue)
                {
                    ViewBag.Message = "يرجى اختيار مطعم لعرض تقرير المبيعات";
                    return View();
                }

                // Get restaurant info
                var restaurant = await _context.Restaurants
                    .FirstOrDefaultAsync(r => r.Id == restaurantId.Value);

                if (restaurant == null)
                {
                    TempData["ErrorMessage"] = "المطعم المحدد غير موجود";
                    return View();
                }

                // Get sales data for the restaurant
                var salesQuery = from oi in _context.OrderItems
                                 join o in _context.Orders on oi.OrderId equals o.OrderId
                                 join p in _context.Products on oi.ProductId equals p.Id
                                 join off in _context.Offers on oi.OfferId equals off.Id
                                 where oi.RestId == restaurantId.Value
                                 && o.CreatedAt >= startDate && o.CreatedAt <= endDate
                                 && o.Status == "completed"
                                 select new
                                 {
                                     OrderId = o.OrderId,
                                     OrderDate = o.CreatedAt,
                                     ProductName = p.Name,
                                     OfferName = off.Name,
                                     UnitPrice = oi.UnitPrice,
                                     Quantity = oi.Quantity,
                                     TotalPrice = oi.TotalPrice,
                                     CustomerNotes = o.CustomerNotes,
                                     PaymentType = o.PaymentType
                                 };

                var salesData = await salesQuery.ToListAsync();

                // Calculate totals
                var totalSales = salesData.Sum(x => x.TotalPrice);
                var totalOrders = salesData.Select(x => x.OrderId).Distinct().Count();
                var totalItems = salesData.Sum(x => x.Quantity);
                var averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

                // Group by product
                var salesByProduct = salesData
                    .GroupBy(x => x.ProductName)
                    .Select(g => new
                    {
                        ProductName = g.Key,
                        TotalQuantity = g.Sum(x => x.Quantity),
                        TotalSales = g.Sum(x => x.TotalPrice),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .ToList();

                // Group by date
                var salesByDate = salesData
                    .GroupBy(x => x.OrderDate.Value.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalSales = g.Sum(x => x.TotalPrice),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count(),
                        ItemCount = g.Sum(x => x.Quantity)
                    })
                    .OrderBy(x => x.Date)
                    .ToList();

                ViewBag.Restaurant = restaurant;
                ViewBag.TotalSales = totalSales;
                ViewBag.TotalOrders = totalOrders;
                ViewBag.TotalItems = totalItems;
                ViewBag.AverageOrderValue = averageOrderValue;
                ViewBag.SalesByProduct = salesByProduct;
                ViewBag.SalesByDate = salesByDate;
                ViewBag.SalesData = salesData.OrderByDescending(x => x.OrderDate).ToList();

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating restaurant sales report for restaurant {RestaurantId}", restaurantId);
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء تقرير مبيعات المطعم";
                return RedirectToAction("Index", "Home");
            }
        }
    }
}
