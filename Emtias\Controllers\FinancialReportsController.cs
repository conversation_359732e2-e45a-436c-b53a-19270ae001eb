using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;
using Microsoft.AspNetCore.Authorization;

namespace Emtias.Controllers
{
    // [Authorize]
    public class FinancialReportsController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly ILogger<FinancialReportsController> _logger;

        public FinancialReportsController(EmtiazDbContext context, ILogger<FinancialReportsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: FinancialReports/Revenue
        public async Task<IActionResult> Revenue(DateTime? startDate, DateTime? endDate, int? restaurantId, int page = 1, int pageSize = 10)
        {
            try
            {
                // Set default date range if not provided (last 30 days)
                var defaultEndDate = DateTime.Now.Date;
                var defaultStartDate = defaultEndDate.AddDays(-30);

                startDate ??= defaultStartDate;
                endDate ??= defaultEndDate;

                // Ensure end date includes the full day
                endDate = endDate.Value.Date.AddDays(1).AddTicks(-1);

                _logger.LogInformation($"Revenue report: StartDate={startDate}, EndDate={endDate}, RestaurantId={restaurantId}");

                // Get revenue data - remove status filter to get all orders
                var revenueQuery = from oi in _context.OrderItems
                                   join o in _context.Orders on oi.OrderId equals o.OrderId
                                   join r in _context.Restaurants on oi.RestId equals r.Id
                                   join p in _context.Products on oi.ProductId equals p.Id
                                   join off in _context.Offers on oi.OfferId equals off.Id
                                   where o.CreatedAt >= startDate && o.CreatedAt <= endDate
                                   // Remove status filter or use different status values
                                   && (o.Status == "completed" || o.Status == "delivered" || o.Status == "paid" || o.Status == "confirmed")
                                   select new
                                   {
                                       OrderId = o.OrderId,
                                       RestaurantId = r.Id,
                                       RestaurantName = r.Name,
                                       ProductName = p.Name,
                                       OfferName = off.Name,
                                       OrderDate = o.CreatedAt,
                                       TotalPrice = oi.TotalPrice,
                                       Quantity = oi.Quantity,
                                       UnitPrice = oi.UnitPrice,
                                       CommissionRate = r.ApplicationCommission,
                                       CommissionAmount = (oi.TotalPrice * (decimal)(r.ApplicationCommission / 100)),
                                       PaymentType = o.PaymentType,
                                       OrderStatus = o.Status
                                   };

                // Filter by restaurant if specified
                if (restaurantId.HasValue)
                {
                    revenueQuery = revenueQuery.Where(x => x.RestaurantId == restaurantId.Value);
                }

                var revenueData = await revenueQuery.ToListAsync();

                _logger.LogInformation($"Found {revenueData.Count} revenue records");

                // Calculate totals
                var totalRevenue = revenueData.Sum(x => x.CommissionAmount);
                var totalOrders = revenueData.Select(x => x.OrderId).Distinct().Count();
                var totalSales = revenueData.Sum(x => x.TotalPrice);

                // Group by restaurant
                var revenueByRestaurant = revenueData
                    .GroupBy(x => new { x.RestaurantId, x.RestaurantName, x.CommissionRate })
                    .Select(g => new
                    {
                        RestaurantId = g.Key.RestaurantId,
                        RestaurantName = g.Key.RestaurantName,
                        CommissionRate = g.Key.CommissionRate,
                        TotalSales = g.Sum(x => x.TotalPrice),
                        TotalCommission = g.Sum(x => x.CommissionAmount),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderByDescending(x => x.TotalCommission)
                    .ToList();

                // Group by date for chart
                var revenueByDate = revenueData
                    .GroupBy(x => x.OrderDate.Value.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalRevenue = g.Sum(x => x.CommissionAmount),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderBy(x => x.Date)
                    .ToList();

                // Detailed revenue data with pagination
                var detailedRevenueQuery = revenueData
                    .OrderByDescending(x => x.OrderDate)
                    .AsQueryable();

                var totalRecords = revenueData.Count;
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                var pagedRevenueDetails = detailedRevenueQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // Get restaurants for dropdown
                var restaurants = await _context.Restaurants
                    .Where(r => !r.Deleted)
                    .Select(r => new { r.Id, r.Name })
                    .OrderBy(r => r.Name)
                    .ToListAsync();

                ViewBag.Restaurants = restaurants;
                ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
                ViewBag.EndDate = endDate.Value.Date.ToString("yyyy-MM-dd");
                ViewBag.SelectedRestaurantId = restaurantId;
                ViewBag.TotalRevenue = totalRevenue;
                ViewBag.TotalOrders = totalOrders;
                ViewBag.TotalSales = totalSales;
                ViewBag.RevenueByRestaurant = revenueByRestaurant;
                ViewBag.RevenueByDate = revenueByDate;
                ViewBag.RevenueDetails = pagedRevenueDetails;

                // Pagination data
                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = totalPages;
                ViewBag.TotalRecords = totalRecords;
                ViewBag.PageSize = pageSize;
                ViewBag.HasPreviousPage = page > 1;
                ViewBag.HasNextPage = page < totalPages;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating revenue report");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء تقرير الإيرادات";
                return RedirectToAction("Index", "Home");
            }
        }

        // GET: FinancialReports/RestaurantSales
        public async Task<IActionResult> RestaurantSales(int? restaurantId, DateTime? startDate, DateTime? endDate, int page = 1, int pageSize = 10)
        {
            try
            {
                // Set default date range if not provided (last 30 days)
                var defaultEndDate = DateTime.Now.Date;
                var defaultStartDate = defaultEndDate.AddDays(-30);

                startDate ??= defaultStartDate;
                endDate ??= defaultEndDate;

                // Ensure end date includes the full day
                endDate = endDate.Value.Date.AddDays(1).AddTicks(-1);

                _logger.LogInformation($"Restaurant sales report: RestaurantId={restaurantId}, StartDate={startDate}, EndDate={endDate}");

                // Get restaurants for dropdown
                var restaurants = await _context.Restaurants
                    .Where(r => !r.Deleted)
                    .Select(r => new { r.Id, r.Name })
                    .OrderBy(r => r.Name)
                    .ToListAsync();

                ViewBag.Restaurants = restaurants;
                ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
                ViewBag.EndDate = endDate.Value.Date.ToString("yyyy-MM-dd");
                ViewBag.SelectedRestaurantId = restaurantId;

                if (!restaurantId.HasValue)
                {
                    ViewBag.Message = "يرجى اختيار مطعم لعرض تقرير المبيعات";
                    return View();
                }

                // Get restaurant info
                var restaurant = await _context.Restaurants
                    .FirstOrDefaultAsync(r => r.Id == restaurantId.Value);

                if (restaurant == null)
                {
                    TempData["ErrorMessage"] = "المطعم المحدد غير موجود";
                    return View();
                }

                // Get sales data for the restaurant
                var salesQuery = from oi in _context.OrderItems
                                 join o in _context.Orders on oi.OrderId equals o.OrderId
                                 join p in _context.Products on oi.ProductId equals p.Id
                                 join off in _context.Offers on oi.OfferId equals off.Id
                                 where oi.RestId == restaurantId.Value
                                 && o.CreatedAt >= startDate && o.CreatedAt <= endDate
                                 // Remove or modify status filter
                                 && (o.Status == "completed" || o.Status == "delivered" || o.Status == "paid" || o.Status == "confirmed")
                                 select new
                                 {
                                     OrderId = o.OrderId,
                                     OrderDate = o.CreatedAt,
                                     ProductName = p.Name,
                                     OfferName = off.Name,
                                     UnitPrice = oi.UnitPrice,
                                     Quantity = oi.Quantity,
                                     TotalPrice = oi.TotalPrice,
                                     CustomerNotes = o.CustomerNotes,
                                     PaymentType = o.PaymentType,
                                     OrderStatus = o.Status
                                 };

                var salesData = await salesQuery.ToListAsync();

                _logger.LogInformation($"Found {salesData.Count} sales records for restaurant {restaurantId}");

                // Calculate totals
                var totalSales = salesData.Sum(x => x.TotalPrice);
                var totalOrders = salesData.Select(x => x.OrderId).Distinct().Count();
                var totalItems = salesData.Sum(x => x.Quantity);
                var averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

                // Pagination for detailed sales data
                var totalRecords = salesData.Count;
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                var pagedSalesData = salesData
                    .OrderByDescending(x => x.OrderDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                // Group by product
                var salesByProduct = salesData
                    .GroupBy(x => x.ProductName)
                    .Select(g => new
                    {
                        ProductName = g.Key,
                        TotalQuantity = g.Sum(x => x.Quantity),
                        TotalSales = g.Sum(x => x.TotalPrice),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count()
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .ToList();

                // Group by date
                var salesByDate = salesData
                    .GroupBy(x => x.OrderDate.Value.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalSales = g.Sum(x => x.TotalPrice),
                        OrderCount = g.Select(x => x.OrderId).Distinct().Count(),
                        ItemCount = g.Sum(x => x.Quantity)
                    })
                    .OrderBy(x => x.Date)
                    .ToList();

                ViewBag.Restaurant = restaurant;
                ViewBag.TotalSales = totalSales;
                ViewBag.TotalOrders = totalOrders;
                ViewBag.TotalItems = totalItems;
                ViewBag.AverageOrderValue = averageOrderValue;
                ViewBag.SalesByProduct = salesByProduct;
                ViewBag.SalesByDate = salesByDate;
                ViewBag.SalesData = pagedSalesData;

                // Pagination data
                ViewBag.CurrentPage = page;
                ViewBag.TotalPages = totalPages;
                ViewBag.TotalRecords = totalRecords;
                ViewBag.PageSize = pageSize;
                ViewBag.HasPreviousPage = page > 1;
                ViewBag.HasNextPage = page < totalPages;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating restaurant sales report for restaurant {RestaurantId}", restaurantId);
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء تقرير مبيعات المطعم";
                return RedirectToAction("Index", "Home");
            }
        }
    }
}
