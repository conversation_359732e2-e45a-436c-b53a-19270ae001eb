@model IEnumerable<Emtias.Models.Content>

@{
    ViewData["Title"] = "إدارة المحتوى";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>إدارة المحتوى</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item active">المحتوى</li>
        </ol>
    </nav>
</div>

<!-- Main Content -->
<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Header with Add Button -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title">قائمة المحتويات</h5>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            إضافة محتوى جديد
                        </a>
                    </div>

                    <!-- Success/Error Messages -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في المحتويات...">
                            </div>
                        </div>
                    </div>

                    <!-- Table View -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="contentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 60px;">الرقم</th>
                                    <th>العنوان</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر تحديث</th>
                                    <th style="width: 200px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var item in Model)
                                    {
                                        <tr data-title="@item.Title">
                                            <td class="text-center">
                                                <strong>#@item.Id</strong>
                                            </td>
                                            <td>
                                                <strong>@item.Title</strong>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    <i class="bi bi-calendar me-1"></i>
                                                    @item.CreatedAt.ToString("dd/MM/yyyy")
                                                </span>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>
                                                    @item.CreatedAt.ToString("hh:mm tt")
                                                </small>
                                            </td>
                                            <td>
                                                @if (item.UpdatedAt.HasValue)
                                                {
                                                    <span class="text-muted">
                                                        <i class="bi bi-calendar me-1"></i>
                                                        @item.UpdatedAt.Value.ToString("dd/MM/yyyy")
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-clock me-1"></i>
                                                        @item.UpdatedAt.Value.ToString("hh:mm tt")
                                                    </small>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">لم يتم التحديث</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-warning btn-sm" title="تعديل">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-outline-danger btn-sm" title="حذف">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="5" class="text-center py-5">
                                            <i class="bi bi-inbox" style="font-size: 3rem; color: #ccc;"></i>
                                            <p class="text-muted mt-3">لا توجد محتويات حالياً</p>
                                            <a asp-action="Create" class="btn btn-primary">
                                                <i class="bi bi-plus-circle me-1"></i>
                                                إضافة محتوى جديد
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#contentsTable tbody tr');

            tableRows.forEach(row => {
                const title = row.getAttribute('data-title')?.toLowerCase() || '';
                
                if (title.includes(searchValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    <style>
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .btn-group .btn {
            margin: 0 2px;
        }

        .alert {
            border-left: 4px solid;
        }

        .alert-success {
            border-left-color: #198754;
        }

        .alert-danger {
            border-left-color: #dc3545;
        }
    </style>
}

