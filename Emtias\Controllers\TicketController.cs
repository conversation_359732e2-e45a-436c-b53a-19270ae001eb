using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;
using Microsoft.Extensions.Logging;

namespace Emtias.Controllers
{
    public class TicketController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly ILogger<TicketController> _logger;

        public TicketController(EmtiazDbContext context, ILogger<TicketController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Ticket
        public async Task<IActionResult> Index(string status = "all", string priority = "all")
        {
            try
            {
                var ticketsQuery = _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .Include(t => t.TicketChats)
                    .AsQueryable();

                // Filter by status
                if (status != "all")
                {
                    bool statusBool = status == "open";
                    ticketsQuery = ticketsQuery.Where(t => t.Status == statusBool);
                }

                // Filter by priority
                if (priority != "all")
                {
                    ticketsQuery = ticketsQuery.Where(t => t.Priority == priority);
                }

                var tickets = await ticketsQuery
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync();

                // Statistics for dashboard
                ViewBag.TotalTickets = await _context.Tickets.CountAsync();
                ViewBag.OpenTickets = await _context.Tickets.Where(t => t.Status == true).CountAsync();
                ViewBag.ClosedTickets = await _context.Tickets.Where(t => t.Status == false).CountAsync();
                ViewBag.UnassignedTickets = await _context.Tickets.Where(t => t.AssignedTo == null).CountAsync();
                ViewBag.HighPriorityTickets = await _context.Tickets.Where(t => t.Priority == "high").CountAsync();

                ViewBag.CurrentStatus = status;
                ViewBag.CurrentPriority = priority;

                return View(tickets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading tickets");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل التذاكر";
                return View(new List<Ticket>());
            }
        }

        // GET: Ticket/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var ticket = await _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .Include(t => t.TicketChats)
                        .ThenInclude(tc => tc.User)
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (ticket == null)
                {
                    return NotFound();
                }

                // Get support team users for assignment
                ViewBag.SupportUsers = new SelectList(
                    await _context.AspNetUsers
                        .Where(u => u.Active && (u.UserType == 2 || u.UserType == 3)) // Admin or Supervisor
                        .Select(u => new { u.Id, DisplayName = u.FullName ?? u.UserName })
                        .ToListAsync(),
                    "Id", "DisplayName", ticket.AssignedTo);

                return View(ticket);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading ticket details for ID: {TicketId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل تفاصيل التذكرة";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Ticket/Create
        public IActionResult Create()
        {
            // Get all active users for ticket creation
            ViewBag.Users = new SelectList(
                _context.AspNetUsers
                    .Where(u => u.Active)
                    .Select(u => new { u.Id, DisplayName = u.FullName ?? u.UserName })
                    .ToList(),
                "Id", "DisplayName");

            return View();
        }

        // POST: Ticket/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Title,Description,Priority,UserId")] Ticket ticket)
        {
            _logger.LogInformation($"Create POST called with: Title={ticket?.Title}, UserId={ticket?.UserId}, Priority={ticket?.Priority}");

            // Log ModelState errors for debugging
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("ModelState is invalid:");
                foreach (var error in ModelState)
                {
                    _logger.LogWarning($"Key: {error.Key}, Errors: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                }
            }

            if (ModelState.IsValid)
            {
                try
                {
                    ticket.Status = true; // Open by default
                    ticket.CreatedAt = DateTime.Now;
                    ticket.UpdatedAt = null;
                    ticket.AssignedTo = null; // Will be assigned later
                    ticket.Vote = 0;

                    _logger.LogInformation($"Adding ticket to context: {ticket.Title}");
                    _context.Add(ticket);

                    _logger.LogInformation("Saving changes to database...");
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"Ticket created successfully with ID: {ticket.Id}");

                    TempData["SuccessMessage"] = $"تم إنشاء التذكرة '{ticket.Title}' بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating ticket");
                    TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء التذكرة. يرجى المحاولة مرة أخرى.";
                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء التذكرة. يرجى المحاولة مرة أخرى.");
                }
            }
            else
            {
                _logger.LogWarning("Validation failed, returning to view");

                // جمع جميع رسائل الأخطاء
                var errorMessages = new List<string>();
                foreach (var modelError in ModelState)
                {
                    foreach (var error in modelError.Value.Errors)
                    {
                        errorMessages.Add(error.ErrorMessage);
                    }
                }

                if (errorMessages.Any())
                {
                    TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء التالية:<br>" + string.Join("<br>", errorMessages);
                }
                else
                {
                    TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء في النموذج";
                }
            }

            // Reload users for dropdown
            ViewBag.Users = new SelectList(
                _context.AspNetUsers
                    .Where(u => u.Active)
                    .Select(u => new { u.Id, DisplayName = u.FullName ?? u.UserName })
                    .ToList(),
                "Id", "DisplayName", ticket?.UserId);

            return View(ticket);
        }

        // POST: Ticket/Assign
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Assign(int id, string assignedTo)
        {
            try
            {
                var ticket = await _context.Tickets.FindAsync(id);
                if (ticket == null)
                {
                    return NotFound();
                }

                if (string.IsNullOrWhiteSpace(assignedTo))
                {
                    TempData["ErrorMessage"] = "يجب اختيار مستخدم لتعيين التذكرة إليه";
                    return RedirectToAction(nameof(Details), new { id });
                }

                // Verify the assigned user exists and is active
                var assignedUser = await _context.AspNetUsers
                    .FirstOrDefaultAsync(u => u.Id == assignedTo && u.Active);

                if (assignedUser == null)
                {
                    TempData["ErrorMessage"] = "المستخدم المحدد غير موجود أو غير نشط";
                    return RedirectToAction(nameof(Details), new { id });
                }

                ticket.AssignedTo = assignedTo;
                ticket.UpdatedAt = DateTime.Now;

                _context.Update(ticket);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم تعيين التذكرة إلى {assignedUser.FullName ?? assignedUser.UserName} بنجاح";
                _logger.LogInformation($"Ticket {id} assigned to user {assignedTo}");

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning ticket {TicketId} to user {UserId}", id, assignedTo);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تعيين التذكرة";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // POST: Ticket/UpdateStatus
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, bool status)
        {
            try
            {
                var ticket = await _context.Tickets.FindAsync(id);
                if (ticket == null)
                {
                    return NotFound();
                }

                ticket.Status = status;
                ticket.UpdatedAt = DateTime.Now;

                _context.Update(ticket);
                await _context.SaveChangesAsync();

                string statusText = status ? "مفتوحة" : "مغلقة";
                TempData["SuccessMessage"] = $"تم تحديث حالة التذكرة إلى '{statusText}' بنجاح";
                _logger.LogInformation($"Ticket {id} status updated to {status}");

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ticket status for ID: {TicketId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث حالة التذكرة";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // POST: Ticket/UpdatePriority
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdatePriority(int id, string priority)
        {
            try
            {
                var ticket = await _context.Tickets.FindAsync(id);
                if (ticket == null)
                {
                    return NotFound();
                }

                if (string.IsNullOrWhiteSpace(priority))
                {
                    TempData["ErrorMessage"] = "يجب تحديد الأولوية";
                    return RedirectToAction(nameof(Details), new { id });
                }

                ticket.Priority = priority;
                ticket.UpdatedAt = DateTime.Now;

                _context.Update(ticket);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم تحديث أولوية التذكرة إلى '{priority}' بنجاح";
                _logger.LogInformation($"Ticket {id} priority updated to {priority}");

                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ticket priority for ID: {TicketId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث أولوية التذكرة";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        private bool TicketExists(int id)
        {
            return _context.Tickets.Any(e => e.Id == id);
        }
    }
}
