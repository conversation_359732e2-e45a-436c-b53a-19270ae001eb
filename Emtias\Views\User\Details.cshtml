@model Emtias.Models.AspNetUser

@{
    ViewData["Title"] = "تفاصيل المستخدم";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>تفاصيل المستخدم</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المستخدمين</a></li>
            <li class="breadcrumb-item active">التفاصيل</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">@Model.FullName</h5>

                    <!-- User Profile Header -->
                    <div class="row mb-4">
                        <!-- User Image -->
                        <div class="col-md-3 text-center">
                            <div class="user-image-container mb-3">
                                @if (!string.IsNullOrEmpty(Model.Image))
                                {
                                    <img src="@Model.Image" alt="@Model.FullName" class="rounded-circle shadow-lg" style="width: 150px; height: 150px; object-fit: cover;" onerror="showDefaultUserImage(this)">
                                }
                                else
                                {
                                    <div class="user-image-placeholder rounded-circle shadow-lg d-flex align-items-center justify-content-center mx-auto" style="width: 150px; height: 150px; background-color: #e9ecef; border: 1px solid #dee2e6;">
                                        <i class="bi bi-person text-muted" style="font-size: 4rem;"></i>
                                    </div>
                                }
                            </div>
                            <h5 class="mb-1">@Model.FullName</h5>
                            <p class="text-muted">@Model.UserName</p>
                            
                            <!-- Status Badge -->
                            @if (Model.LockoutEnd.HasValue && Model.LockoutEnd > DateTimeOffset.Now)
                            {
                                <span class="badge bg-danger fs-6">محظور</span>
                            }
                            else if (Model.Active)
                            {
                                <span class="badge bg-success fs-6">نشط</span>
                            }
                            else
                            {
                                <span class="badge bg-warning fs-6">غير نشط</span>
                            }
                        </div>

                        <!-- User Quick Info -->
                        <div class="col-md-9">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <i class="bi bi-envelope me-2"></i>
                                                البريد الإلكتروني
                                            </h6>
                                            <p class="card-text">@Model.Email</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-success">
                                                <i class="bi bi-phone me-2"></i>
                                                رقم الهاتف
                                            </h6>
                                            <p class="card-text">@(Model.PhoneNumber ?? "غير محدد")</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-info">
                                                <i class="bi bi-person-badge me-2"></i>
                                                نوع المستخدم
                                            </h6>
                                            <p class="card-text">
                                                @switch (Model.UserType)
                                                {
                                                    case 1:
                                                        <span class="badge bg-info">مستخدم عادي</span>
                                                        break;
                                                    case 2:
                                                        <span class="badge bg-warning">مدير</span>
                                                        break;
                                                    case 3:
                                                        <span class="badge bg-success">مشرف</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">غير محدد</span>
                                                        break;
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning">
                                                <i class="bi bi-calendar me-2"></i>
                                                تاريخ التسجيل
                                            </h6>
                                            <p class="card-text">@(Model.LockoutEnd?.ToString("dd/MM/yyyy") ?? "غير محدد")</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Information -->
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="bi bi-person-lines-fill me-2"></i>
                                        المعلومات الشخصية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label class="form-label fw-bold">رقم الهوية:</label>
                                            <p class="">@(Model.IdentityNo ?? "غير محدد")</p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">تاريخ الميلاد:</label>
                                            <p class="">@(Model.BirthDate?.ToString("dd/MM/yyyy") ?? "غير محدد")</p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">الجنس:</label>
                                            <p class="">@(Model.Gender ?? "غير محدد")</p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">البيانات مكتملة:</label>
                                            <p class="">
                                                @if (Model.HasComplateData)
                                                {
                                                    <span class="badge bg-success">نعم</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">لا</span>
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="bi bi-shield-check me-2"></i>
                                        معلومات الحساب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label class="form-label fw-bold">البريد مؤكد:</label>
                                            <p class="">
                                                @if (Model.EmailConfirmed)
                                                {
                                                    <span class="badge bg-success">مؤكد</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير مؤكد</span>
                                                }
                                            </p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">الهاتف مؤكد:</label>
                                            <p class="">
                                                @if (Model.PhoneNumberConfirmed)
                                                {
                                                    <span class="badge bg-success">مؤكد</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير مؤكد</span>
                                                }
                                            </p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">المصادقة الثنائية:</label>
                                            <p class="">
                                                @if (Model.TwoFactorEnabled)
                                                {
                                                    <span class="badge bg-success">مفعلة</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير مفعلة</span>
                                                }
                                            </p>
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">محاولات الدخول الفاشلة:</label>
                                            <p class="">
                                                <span class="badge bg-info">@Model.AccessFailedCount</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Roles -->
                    @if (Model.Roles.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="bi bi-people me-2"></i>
                                            الأدوار والصلاحيات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @foreach (var role in Model.Roles)
                                        {
                                            <span class="badge bg-primary me-2 mb-2 fs-6">@role.Name</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="text-center mt-4">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                            <i class="bi bi-pencil me-1"></i>
                            تعديل
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger me-2">
                            <i class="bi bi-trash me-1"></i>
                            حذف
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Show default user image on error
    function showDefaultUserImage(img) {
        img.style.display = 'none';
        const placeholder = img.parentElement.querySelector('.user-image-placeholder') ||
                          img.parentElement.appendChild(document.createElement('div'));
        placeholder.className = 'user-image-placeholder rounded-circle shadow-lg d-flex align-items-center justify-content-center mx-auto';
        placeholder.style.cssText = 'width: 150px; height: 150px; background-color: #e9ecef; border: 1px solid #dee2e6;';
        placeholder.innerHTML = '<i class="bi bi-person text-muted" style="font-size: 4rem;"></i>';
    }
</script>
