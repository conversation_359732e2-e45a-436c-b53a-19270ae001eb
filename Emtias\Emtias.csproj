<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-Emtias-7a97b02c-3747-4a36-a487-da723f0e0c49</UserSecretsId>
  </PropertyGroup>
	<ItemGroup>
		<None Update="web.config">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<Target Name="CopyWebConfig" AfterTargets="AfterPublish">
		<Copy SourceFiles="$(ProjectDir)web.config" DestinationFolder="$(PublishDir)" />
	</Target>

	<ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.12" />
 <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.12">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.7" />
  </ItemGroup>
	<ItemGroup>
		<Content Update="web.config">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
</Project>
