using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;

namespace Emtias.Controllers
{
    public class ContentController : Controller
    {
        private readonly EmtiazDbContext _context;

        public ContentController(EmtiazDbContext context)
        {
            _context = context;
        }

        // GET: Content
        public async Task<IActionResult> Index()
        {
            var contents = await _context.Contents
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();

            return View(contents);
        }

        // GET: Content/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var content = await _context.Contents
                .FirstOrDefaultAsync(m => m.Id == id);

            if (content == null)
            {
                return NotFound();
            }

            return View(content);
        }

        // GET: Content/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Content/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Title,Content1")] Content content)
        {
            try
            {
                // Custom validation
                if (string.IsNullOrWhiteSpace(content.Title))
                {
                    ModelState.AddModelError("Title", "العنوان مطلوب ولا يمكن أن يكون فارغاً");
                }

                if (string.IsNullOrWhiteSpace(content.Content1))
                {
                    ModelState.AddModelError("Content1", "المحتوى مطلوب ولا يمكن أن يكون فارغاً");
                }

                if (ModelState.IsValid)
                {
                    content.CreatedAt = DateTime.Now;
                    content.UpdatedAt = null;

                    _context.Add(content);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم إنشاء المحتوى '{content.Title}' بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء إنشاء المحتوى: {ex.Message}";
                ModelState.AddModelError("", $"فشل في إنشاء المحتوى. تفاصيل الخطأ: {ex.Message}");
            }

            return View(content);
        }

        // GET: Content/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var content = await _context.Contents.FindAsync(id);
            if (content == null)
            {
                return NotFound();
            }

            return View(content);
        }

        // POST: Content/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Title,Content1,CreatedAt")] Content content)
        {
            if (id != content.Id)
            {
                return NotFound();
            }

            try
            {
                // Custom validation
                if (string.IsNullOrWhiteSpace(content.Title))
                {
                    ModelState.AddModelError("Title", "العنوان مطلوب ولا يمكن أن يكون فارغاً");
                }

                if (string.IsNullOrWhiteSpace(content.Content1))
                {
                    ModelState.AddModelError("Content1", "المحتوى مطلوب ولا يمكن أن يكون فارغاً");
                }

                if (ModelState.IsValid)
                {
                    content.UpdatedAt = DateTime.Now;

                    _context.Update(content);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم تحديث المحتوى '{content.Title}' بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ContentExists(content.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء تحديث المحتوى: {ex.Message}";
                ModelState.AddModelError("", $"فشل في تحديث المحتوى. تفاصيل الخطأ: {ex.Message}");
            }

            return View(content);
        }

        // GET: Content/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var content = await _context.Contents
                .FirstOrDefaultAsync(m => m.Id == id);

            if (content == null)
            {
                return NotFound();
            }

            return View(content);
        }

        // POST: Content/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var content = await _context.Contents.FindAsync(id);
                if (content != null)
                {
                    _context.Contents.Remove(content);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم حذف المحتوى '{content.Title}' بنجاح";
                }
                else
                {
                    TempData["ErrorMessage"] = "المحتوى غير موجود";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"حدث خطأ أثناء حذف المحتوى: {ex.Message}";
                return RedirectToAction(nameof(Delete), new { id });
            }
        }

        private bool ContentExists(int id)
        {
            return _context.Contents.Any(e => e.Id == id);
        }
    }
}

