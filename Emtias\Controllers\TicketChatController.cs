using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Emtias.Models;
using Microsoft.Extensions.Logging;

namespace Emtias.Controllers
{
    public class TicketChatController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly ILogger<TicketChatController> _logger;

        public TicketChatController(EmtiazDbContext context, ILogger<TicketChatController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: TicketChat/Chat/5
        public async Task<IActionResult> Chat(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var ticket = await _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .Include(t => t.TicketChats)
                        .ThenInclude(tc => tc.User)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (ticket == null)
                {
                    return NotFound();
                }

                // Check if ticket is assigned
                if (ticket.AssignedTo == null)
                {
                    TempData["ErrorMessage"] = "لا يمكن فتح الدردشة. يجب تعيين التذكرة أولاً";
                    return RedirectToAction("Details", "Ticket", new { id });
                }

                // Order chat messages by creation time
                ticket.TicketChats = ticket.TicketChats.OrderBy(tc => tc.CreatedAt).ToList();

                return View(ticket);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading chat for ticket ID: {TicketId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الدردشة";
                return RedirectToAction("Index", "Ticket");
            }
        }

        // POST: TicketChat/SendMessage
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendMessage(int ticketId, string message, string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    TempData["ErrorMessage"] = "لا يمكن إرسال رسالة فارغة";
                    return RedirectToAction(nameof(Chat), new { id = ticketId });
                }

                if (string.IsNullOrWhiteSpace(userId))
                {
                    TempData["ErrorMessage"] = "يجب تحديد المستخدم المرسل";
                    return RedirectToAction(nameof(Chat), new { id = ticketId });
                }

                // Verify ticket exists and is assigned
                var ticket = await _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .FirstOrDefaultAsync(t => t.Id == ticketId);

                if (ticket == null)
                {
                    return NotFound();
                }

                if (ticket.AssignedTo == null)
                {
                    TempData["ErrorMessage"] = "لا يمكن إرسال رسالة. التذكرة غير معينة";
                    return RedirectToAction(nameof(Chat), new { id = ticketId });
                }

                // Verify user exists and is active
                var user = await _context.AspNetUsers
                    .FirstOrDefaultAsync(u => u.Id == userId && u.Active);

                if (user == null)
                {
                    TempData["ErrorMessage"] = "المستخدم غير موجود أو غير نشط";
                    return RedirectToAction(nameof(Chat), new { id = ticketId });
                }

                // Check if user is authorized to send messages (ticket owner or assigned support)
                if (userId != ticket.UserId && userId != ticket.AssignedTo)
                {
                    TempData["ErrorMessage"] = "غير مصرح لك بإرسال رسائل في هذه التذكرة";
                    return RedirectToAction(nameof(Chat), new { id = ticketId });
                }

                // Create new chat message
                var ticketChat = new TicketChat
                {
                    TicketId = ticketId,
                    UserId = userId,
                    Message = message.Trim(),
                    CreatedAt = DateTime.Now
                };

                _context.TicketChats.Add(ticketChat);

                // Update ticket's UpdatedAt timestamp
                ticket.UpdatedAt = DateTime.Now;
                _context.Update(ticket);

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم إرسال الرسالة بنجاح";
                _logger.LogInformation($"Message sent in ticket {ticketId} by user {userId}");

                return RedirectToAction(nameof(Chat), new { id = ticketId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message in ticket {TicketId} by user {UserId}", ticketId, userId);
                TempData["ErrorMessage"] = "حدث خطأ أثناء إرسال الرسالة";
                return RedirectToAction(nameof(Chat), new { id = ticketId });
            }
        }

        // GET: TicketChat/GetMessages/5 (AJAX endpoint)
        [HttpGet]
        public async Task<IActionResult> GetMessages(int ticketId, DateTime? lastMessageTime = null)
        {
            try
            {
                var query = _context.TicketChats
                    .Include(tc => tc.User)
                    .Where(tc => tc.TicketId == ticketId);

                // If lastMessageTime is provided, get only newer messages
                if (lastMessageTime.HasValue)
                {
                    query = query.Where(tc => tc.CreatedAt > lastMessageTime.Value);
                }

                var messages = await query
                    .OrderBy(tc => tc.CreatedAt)
                    .Select(tc => new
                    {
                        id = tc.Id,
                        message = tc.Message,
                        userId = tc.UserId,
                        userName = tc.User.FullName ?? tc.User.UserName,
                        userImage = tc.User.Image,
                        createdAt = tc.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        createdAtFormatted = tc.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                    })
                    .ToListAsync();

                return Json(new { success = true, messages });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for ticket {TicketId}", ticketId);
                return Json(new { success = false, message = "حدث خطأ أثناء تحميل الرسائل" });
            }
        }

        // POST: TicketChat/SendMessageAjax (AJAX endpoint)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendMessageAjax(int ticketId, string message, string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(message))
                {
                    return Json(new { success = false, message = "لا يمكن إرسال رسالة فارغة" });
                }

                if (string.IsNullOrWhiteSpace(userId))
                {
                    return Json(new { success = false, message = "يجب تحديد المستخدم المرسل" });
                }

                // Verify ticket exists and is assigned
                var ticket = await _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .FirstOrDefaultAsync(t => t.Id == ticketId);

                if (ticket == null)
                {
                    return Json(new { success = false, message = "التذكرة غير موجودة" });
                }

                if (ticket.AssignedTo == null)
                {
                    return Json(new { success = false, message = "لا يمكن إرسال رسالة. التذكرة غير معينة" });
                }

                // Verify user exists and is active
                var user = await _context.AspNetUsers
                    .FirstOrDefaultAsync(u => u.Id == userId && u.Active);

                if (user == null)
                {
                    return Json(new { success = false, message = "المستخدم غير موجود أو غير نشط" });
                }

                // Check if user is authorized to send messages
                if (userId != ticket.UserId && userId != ticket.AssignedTo)
                {
                    return Json(new { success = false, message = "غير مصرح لك بإرسال رسائل في هذه التذكرة" });
                }

                // Create new chat message
                var ticketChat = new TicketChat
                {
                    TicketId = ticketId,
                    UserId = userId,
                    Message = message.Trim(),
                    CreatedAt = DateTime.Now
                };

                _context.TicketChats.Add(ticketChat);

                // Update ticket's UpdatedAt timestamp
                ticket.UpdatedAt = DateTime.Now;
                _context.Update(ticket);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Message sent via AJAX in ticket {ticketId} by user {userId}");

                // Return the new message data
                var messageData = new
                {
                    id = ticketChat.Id,
                    message = ticketChat.Message,
                    userId = ticketChat.UserId,
                    userName = user.FullName ?? user.UserName,
                    userImage = user.Image,
                    createdAt = ticketChat.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    createdAtFormatted = ticketChat.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                };

                return Json(new { success = true, message = "تم إرسال الرسالة بنجاح", data = messageData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message via AJAX in ticket {TicketId} by user {UserId}", ticketId, userId);
                return Json(new { success = false, message = "حدث خطأ أثناء إرسال الرسالة" });
            }
        }

        // GET: TicketChat/GetTicketInfo/5 (AJAX endpoint)
        [HttpGet]
        public async Task<IActionResult> GetTicketInfo(int ticketId)
        {
            try
            {
                var ticket = await _context.Tickets
                    .Include(t => t.User)
                    .Include(t => t.AssignedToNavigation)
                    .Select(t => new
                    {
                        id = t.Id,
                        title = t.Title,
                        description = t.Description,
                        status = t.Status,
                        priority = t.Priority,
                        createdAt = t.CreatedAt.ToString("dd/MM/yyyy HH:mm"),
                        updatedAt = t.UpdatedAt.HasValue ? t.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm") : null,
                        user = new
                        {
                            id = t.User.Id,
                            name = t.User.FullName ?? t.User.UserName,
                            email = t.User.Email,
                            image = t.User.Image
                        },
                        assignedTo = t.AssignedToNavigation != null ? new
                        {
                            id = t.AssignedToNavigation.Id,
                            name = t.AssignedToNavigation.FullName ?? t.AssignedToNavigation.UserName,
                            email = t.AssignedToNavigation.Email,
                            image = t.AssignedToNavigation.Image
                        } : null,
                        messagesCount = t.TicketChats.Count
                    })
                    .FirstOrDefaultAsync(t => t.id == ticketId);

                if (ticket == null)
                {
                    return Json(new { success = false, message = "التذكرة غير موجودة" });
                }

                return Json(new { success = true, data = ticket });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ticket info for ID: {TicketId}", ticketId);
                return Json(new { success = false, message = "حدث خطأ أثناء تحميل معلومات التذكرة" });
            }
        }
    }
}
