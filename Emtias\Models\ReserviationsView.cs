﻿using System;
using System.Collections.Generic;

namespace Emtias.Models;

public partial class ReserviationsView
{
    public int Id { get; set; }

    public DateOnly? ReserveDate { get; set; }

    public TimeOnly? ReserveTime { get; set; }

    public int? RestId { get; set; }

    public int? Chairs { get; set; }

    public DateTime? CreateAt { get; set; }

    public string? ReserveState { get; set; }

    public string? RestName { get; set; }
}
