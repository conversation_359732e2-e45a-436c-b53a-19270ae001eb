using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System.Security.Cryptography;
using System.Text;

namespace Emtias.Services
{
    
    /// خدمة رفع الملفات الموحدة
   
    public class FileUploadService : IFileUploadService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILogger<FileUploadService> _logger;

        // الصيغ المدعومة للصور
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg" };
        
        // الحد الأقصى لحجم الملف (5MB)
        private const long MaxFileSize = 5 * 1024 * 1024;

        // المجلدات المسموحة
        private readonly string[] _allowedFolders = { "categories", "products", "restaurants" };

        public FileUploadService(IWebHostEnvironment webHostEnvironment, ILogger<FileUploadService> logger)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
        }

       
        /// رفع صورة مع تشفير الاسم
    
        public async Task<FileUploadResult> UploadImageAsync(IFormFile file, string folderName)
        {
            try
            {
                _logger.LogInformation("بدء عملية رفع صورة إلى مجلد: {FolderName}", folderName);

                // التحقق من صحة اسم المجلد
                if (!_allowedFolders.Contains(folderName.ToLowerInvariant()))
                {
                    return new FileUploadResult
                    {
                        Success = false,
                        ErrorMessage = $"اسم المجلد غير مدعوم: {folderName}"
                    };
                }

                // التحقق من صحة الملف
                var validationResult = ValidateImageFile(file);
                if (!validationResult.IsValid)
                {
                    return new FileUploadResult
                    {
                        Success = false,
                        ErrorMessage = validationResult.ErrorMessage
                    };
                }

                // إنشاء اسم مشفر للملف
                var encodedFileName = GenerateEncodedFileName(file.FileName, validationResult.FileExtension!);
                
                // تحديد مسار المجلد
                var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "images", folderName);
                
                // إنشاء المجلد إذا لم يكن موجوداً
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                    _logger.LogInformation("تم إنشاء مجلد جديد: {FolderPath}", uploadsFolder);
                }

                // حفظ الملف
                var filePath = Path.Combine(uploadsFolder, encodedFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }

                // إنشاء المسار النسبي
                var relativePath = $"/images/{folderName}/{encodedFileName}";

                _logger.LogInformation("تم رفع الصورة بنجاح: {RelativePath}", relativePath);

                return new FileUploadResult
                {
                    Success = true,
                    RelativePath = relativePath,
                    OriginalFileName = file.FileName,
                    EncodedFileName = encodedFileName,
                    FileSize = file.Length
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع الصورة إلى مجلد: {FolderName}", folderName);
                return new FileUploadResult
                {
                    Success = false,
                    ErrorMessage = $"خطأ في رفع الملف: {ex.Message}"
                };
            }
        }

 
        /// حذف صورة
       
        public bool DeleteImage(string relativePath)
        {
            try
            {
                if (string.IsNullOrEmpty(relativePath) || relativePath.StartsWith("http"))
                {
                    return false;
                }

                var fullPath = Path.Combine(_webHostEnvironment.WebRootPath, relativePath.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));
                
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation("تم حذف الصورة: {FilePath}", fullPath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الصورة: {RelativePath}", relativePath);
                return false;
            }
        }

      
        /// التحقق من صحة الملف
      
        public FileValidationResult ValidateImageFile(IFormFile file)
        {
            // التحقق من وجود الملف
            if (file == null || file.Length == 0)
            {
                return new FileValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "لم يتم اختيار ملف"
                };
            }

            // التحقق من حجم الملف
            if (file.Length > MaxFileSize)
            {
                return new FileValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "حجم الملف يجب أن يكون أقل من 5 ميجابايت",
                    FileSize = file.Length
                };
            }

            // التحقق من امتداد الملف
            var fileExtension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            if (string.IsNullOrEmpty(fileExtension) || !_allowedExtensions.Contains(fileExtension))
            {
                return new FileValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP, SVG",
                    FileExtension = fileExtension
                };
            }

            return new FileValidationResult
            {
                IsValid = true,
                FileExtension = fileExtension,
                FileSize = file.Length
            };
        }

        /// إنشاء اسم مشفر للملف

        private string GenerateEncodedFileName(string originalFileName, string extension)
        {
            // استخدام الوقت الحالي + GUID + hash من الاسم الأصلي
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var guid = Guid.NewGuid().ToString("N")[..8]; // أول 8 أحرف من GUID
            
            // إنشاء hash من الاسم الأصلي
            var hash = GenerateFileHash(originalFileName);
            
            // تكوين الاسم المشفر
            var encodedName = $"{timestamp}_{guid}_{hash}";
            
            return $"{encodedName}{extension}";
        }

      
        /// إنشاء hash من النص
   
        private string GenerateFileHash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToHexString(hashBytes)[..8]; // أول 8 أحرف من الhash
            }
        }
    }
}
