@model Emtias.Models.Restaurant
@using Emtias.Models
@using System.Collections.Generic

@{
    ViewData["Title"] = "تفاصيل المطعم";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>تفاصيل المطعم</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a href="/">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المطاعم</a></li>
            <li class="breadcrumb-item active">@Model.Name</li>
        </ol>
    </nav>
</div><!-- End Page Title -->

<section class="section">
    <div class="row">
        <!-- Restaurant Header Card -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            @if (!string.IsNullOrEmpty(Model.IconLink))
                            {
                                <img src="@Model.IconLink" alt="@Model.Name" class="restaurant-icon-large rounded-3 shadow" style="width: 120px; height: 120px; object-fit: cover;" onerror="showDefaultIcon(this)">
                            }
                            else
                            {
                                <div class="restaurant-icon-placeholder-large rounded-3 d-flex align-items-center justify-content-center shadow-sm" style="width: 120px; height: 120px; background-color: #f8f9fa; border: 2px dashed #dee2e6;">
                                    <i class="bi bi-shop text-muted" style="font-size: 3rem;"></i>
                                </div>
                            }
                        </div>
                        <div class="col-md-8">
                            <h2 class="mb-2">@Model.Name</h2>
                            @if (!string.IsNullOrEmpty(Model.EnName))
                            {
                                <h5 class="text-muted mb-3">@Model.EnName</h5>
                            }
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                @if (Model.Catgory != null)
                                {
                                    <span class="badge bg-primary fs-6">
                                        <i class="bi bi-tag me-1"></i>
                                        @Model.Catgory.Name
                                    </span>
                                }
                                <span class="badge @(Model.Deleted ? "bg-danger" : "bg-success") fs-6">
                                    <i class="bi bi-@(Model.Deleted ? "x-circle" : "check-circle") me-1"></i>
                                    @(Model.Deleted ? "محذوف" : "نشط")
                                </span>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.Address))
                            {
                                <p class="text-muted mb-2">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    @Model.Address
                                </p>
                            }
                            @if (!string.IsNullOrEmpty(Model.Lat) && !string.IsNullOrEmpty(Model.Lng))
                            {
                                <p class="text-muted mb-2">
                                    <i class="bi bi-pin-map me-2"></i>
                                    الإحداثيات: @Model.Lat, @Model.Lng
                                </p>
                            }
                            <p class="text-muted mb-0">
                                <i class="bi bi-percent me-2"></i>
                                نسبة العمولة: @Model.ApplicationCommission%
                            </p>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="btn-group-vertical w-100" role="group">
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning mb-2">
                                    <i class="bi bi-pencil me-1"></i>
                                    تعديل
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-outline-danger">
                                    <i class="bi bi-trash me-1"></i>
                                    حذف
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="col-12">
            <div class="row">
                <div class="col-md-3">
                    <div class="card info-card sales-card">
                        <div class="card-body">
                            <h5 class="card-title">المنتجات</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-box"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>@(Model.Products?.Count ?? 0)</h6>
                                    <span class="text-success small pt-1 fw-bold">منتج</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card info-card revenue-card">
                        <div class="card-body">
                            <h5 class="card-title">العروض</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-percent"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>@(Model.Offers?.Count ?? 0)</h6>
                                    <span class="text-primary small pt-1 fw-bold">عرض</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card info-card customers-card">
                        <div class="card-body">
                            <h5 class="card-title">الحجوزات</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-calendar2-event"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>@(Model.Reservations?.Count ?? 0)</h6>
                                    <span class="text-danger small pt-1 fw-bold">حجز</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card info-card">
                        <div class="card-body">
                            <h5 class="card-title">التعليقات</h5>
                            <div class="d-flex align-items-center">
                                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="bi bi-chat-dots"></i>
                                </div>
                                <div class="ps-3">
                                    <h6>@ViewBag.TotalComments</h6>
                                    <span class="text-warning small pt-1 fw-bold">تعليق</span>
                                    @if (ViewBag.TotalRatings > 0)
                                    {
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <i class="bi bi-star-fill text-warning"></i>
                                                @ViewBag.AverageRating.ToString("F1")
                                            </small>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Restaurant Details -->
        @if (!string.IsNullOrEmpty(Model.Details))
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-info-circle me-2"></i>
                            تفاصيل المطعم
                        </h5>
                        <p class="card-text">@Model.Details</p>
                    </div>
                </div>
            </div>
        }

        <!-- Location Map (if coordinates available) -->
        @if (!string.IsNullOrEmpty(Model.Lat) && !string.IsNullOrEmpty(Model.Lng))
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-geo-alt me-2"></i>
                            الموقع الجغرافي
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="location-info">
                                    <p><strong>خط العرض:</strong> @Model.Lat</p>
                                    <p><strong>خط الطول:</strong> @Model.Lng</p>
                                    @if (!string.IsNullOrEmpty(Model.Address))
                                    {
                                        <p><strong>العنوان:</strong> @Model.Address</p>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="map-actions">
                                    <a href="https://www.google.com/maps?q=@Model.Lat,@Model.Lng" target="_blank" class="btn btn-outline-primary me-2">
                                        <i class="bi bi-map me-1"></i>
                                        عرض في خرائط جوجل
                                    </a>
                                    <button type="button" class="btn btn-outline-secondary" onclick="copyCoordinates()">
                                        <i class="bi bi-clipboard me-1"></i>
                                        نسخ الإحداثيات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Recent Products -->
        @if (Model.Products != null && Model.Products.Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-box me-2"></i>
                                المنتجات (@(Model.Products?.Count ?? 0))
                            </h5>
                            <a href="#" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                        </div>
                        <div class="row">
                            @foreach (var product in (Model.Products ?? new List<Product>()).Take(6))
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body p-3">
                                            <h6 class="card-title">@product.Name</h6>
                                            @if (!string.IsNullOrEmpty(product.EnName))
                                            {
                                                <p class="card-text text-muted small">@product.EnName</p>
                                            }
                                            
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Restaurant Rating Summary -->
        @if (ViewBag.TotalRatings > 0)
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-star-fill me-2 text-warning"></i>
                            تقييم المطعم
                        </h5>
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center">
                                <div class="rating-display">
                                    <h2 class="display-4 text-warning mb-0">@ViewBag.AverageRating.ToString("F1")</h2>
                                    <div class="stars mb-2">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            if (i <= Math.Floor((double)ViewBag.AverageRating))
                                            {
                                                <i class="bi bi-star-fill text-warning"></i>
                                            }
                                            else if (i <= Math.Ceiling((double)ViewBag.AverageRating))
                                            {
                                                <i class="bi bi-star-half text-warning"></i>
                                            }
                                            else
                                            {
                                                <i class="bi bi-star text-muted"></i>
                                            }
                                        }
                                    </div>
                                    <p class="text-muted">من أصل @ViewBag.TotalRatings تقييم</p>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="rating-breakdown">
                                    @for (int star = 5; star >= 1; star--)
                                    {
                                        var ratingBreakdown = (Dictionary<int, int>)ViewBag.RatingBreakdown;
                                        var starCount = ratingBreakdown.ContainsKey(star) ? ratingBreakdown[star] : 0;
                                        var percentage = ViewBag.TotalRatings > 0 ? (starCount * 100.0 / (int)ViewBag.TotalRatings) : 0;

                                        <div class="d-flex align-items-center mb-2">
                                            <span class="me-2">@star نجوم</span>
                                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                <div class="progress-bar bg-warning" role="progressbar" style="width: @percentage%"></div>
                                            </div>
                                            <span class="text-muted small">@starCount</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

         <!-- Restaurant Offers Section -->
        @if (ViewBag.Offers != null && ((List<Offer>)ViewBag.Offers).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gift me-2"></i>
                                عروض المطعم (@ViewBag.TotalOffers)
                            </h5>
                        </div>

                        <div class="row">
                            @foreach (var offer in (List<Offer>)ViewBag.Offers)
                            {
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">@offer.Name</h6>
                                                @if (offer.Discount > 0)
                                                {
                                                    <span class="badge bg-danger">خصم @offer.Discount%</span>
                                                }
                                            </div>

                                            @if (offer.Product != null)
                                            {
                                                <p class="text-muted small mb-2">
                                                    <i class="bi bi-box me-1"></i>
                                                    @offer.Product.Name
                                                </p>
                                            }

                                            <div class="row text-center mb-3">
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-primary mb-0">@offer.Price ر.س</h6>
                                                        <small class="text-muted">السعر</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-success mb-0">@(offer.Units ?? 0)</h6>
                                                        <small class="text-muted">الكمية</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <h6 class="text-info mb-0">@offer.DeleverCost ر.س</h6>
                                                    <small class="text-muted">التوصيل</small>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar-event me-1"></i>
                                                    من @offer.StartDate?.ToString("dd/MM/yyyy")
                                                </small>
                                                <small class="text-muted">
                                                    إلى @offer.EndDate?.ToString("dd/MM/yyyy")
                                                    <i class="bi bi-calendar-x me-1"></i>
                                                </small>
                                            </div>

                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge @(offer.State == "نشط" ? "bg-success" : "bg-secondary")">
                                                    @offer.State
                                                </span>
                                                <a asp-controller="Offer" asp-action="Details" asp-route-id="@offer.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye me-1"></i>
                                                    عرض التفاصيل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Offers Pagination -->
                        @if (ViewBag.OffersTotalPages > 1)
                        {
                            <nav aria-label="تنقل العروض" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.OffersPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = ViewBag.RatingsPage, offersPage = (int)ViewBag.OffersPage - 1 })">السابق</a>
                                        </li>
                                    }

                                    @for (int i = 1; i <= ViewBag.OffersTotalPages; i++)
                                    {
                                        <li class="page-item @(i == ViewBag.OffersPage ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = ViewBag.RatingsPage, offersPage = i })">@i</a>
                                        </li>
                                    }

                                    @if (ViewBag.OffersPage < ViewBag.OffersTotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = ViewBag.RatingsPage, offersPage = (int)ViewBag.OffersPage + 1 })">التالي</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- No Comments Message -->
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد تعليقات حتى الآن</h5>
                        <p class="text-muted">كن أول من يضع تعليقاً على هذا المطعم</p>
                    </div>
                </div>
            </div>
        }

        <!-- Restaurant Comments Section -->
        @if (ViewBag.Comments != null && ((List<RestaurantsComment>)ViewBag.Comments).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-chat-dots me-2"></i>
                                تعليقات العملاء (@ViewBag.TotalComments)
                            </h5>
                        </div>

                        @foreach (RestaurantsComment comment in (List<RestaurantsComment>)ViewBag.Comments)
                        {
                            <div class="comment-item border rounded p-3 mb-3 bg-light">
                                <div class="d-flex align-items-start">
                                    <div class="avatar-container me-3">
                                        @if (!string.IsNullOrEmpty(comment.User?.Img))
                                        {
                                            <img src="@comment.User.Img" alt="@comment.User.Name" class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                        }
                                        else
                                        {
                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background-color: #6c757d;">
                                                <i class="bi bi-person text-white"></i>
                                            </div>
                                        }
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1">@(comment.User?.Name ?? "مستخدم")</h6>
                                                <small class="text-muted">@comment.CommentDate?.ToString("dd/MM/yyyy HH:mm")</small>
                                            </div>
                                        </div>
                                        <p class="mb-2">@comment.Comment</p>

                                        @{
                                            var commentsWithLikes = (Dictionary<long?, int>)ViewBag.CommentsWithLikes;
                                            var likesCount = commentsWithLikes.ContainsKey(comment.Id) ? commentsWithLikes[comment.Id] : 0;
                                        }
                                        @if (likesCount > 0)
                                        {
                                            <div class="comment-actions">
                                                <small class="text-muted">
                                                    <i class="bi bi-heart-fill text-danger me-1"></i>
                                                    @likesCount إعجاب
                                                </small>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Comments Pagination -->
                        @if (ViewBag.CommentsTotalPages > 1)
                        {
                            <nav aria-label="تنقل التعليقات" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.CommentsPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = (int)ViewBag.CommentsPage - 1, ratingsPage = ViewBag.RatingsPage, offersPage = ViewBag.OffersPage })">السابق</a>
                                        </li>
                                    }

                                    @for (int i = 1; i <= ViewBag.CommentsTotalPages; i++)
                                    {
                                        <li class="page-item @(i == ViewBag.CommentsPage ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = i, ratingsPage = ViewBag.RatingsPage, offersPage = ViewBag.OffersPage })">@i</a>
                                        </li>
                                    }

                                    @if (ViewBag.CommentsPage < ViewBag.CommentsTotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = (int)ViewBag.CommentsPage + 1, ratingsPage = ViewBag.RatingsPage, offersPage = ViewBag.OffersPage })">التالي</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Restaurant Ratings Section -->
        @if (ViewBag.Ratings != null && ((List<RestaurantsCommentsVoting>)ViewBag.Ratings).Any())
        {
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-star-fill me-2 text-warning"></i>
                                تقييمات العملاء (@ViewBag.TotalRatings)
                            </h5>
                        </div>

                        @foreach (RestaurantsCommentsVoting rating in (List<RestaurantsCommentsVoting>)ViewBag.Ratings)
                        {
                            <div class="rating-item border rounded p-3 mb-3 bg-light">
                                <div class="d-flex align-items-start">
                                    <div class="avatar-container me-3">
                                        @if (!string.IsNullOrEmpty(rating.User?.Img))
                                        {
                                            <img src="@rating.User.Img" alt="@rating.User.Name" class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                        }
                                        else
                                        {
                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; background-color: #6c757d;">
                                                <i class="bi bi-person text-white"></i>
                                            </div>
                                        }
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1">@(rating.User?.Name ?? "مستخدم")</h6>
                                                <small class="text-muted">@rating.VotingDate?.ToString("dd/MM/yyyy HH:mm")</small>
                                            </div>
                                            <div class="rating-stars">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    <i class="bi bi-star@(i <= rating.Stars ? "-fill text-warning" : " text-muted")"></i>
                                                }
                                                <span class="ms-2 text-muted">(@rating.Stars/5)</span>
                                            </div>
                                        </div>

                                        @if (rating.RestComment != null && !string.IsNullOrEmpty(rating.RestComment.Comment))
                                        {
                                            <div class="related-comment p-2 bg-white rounded border-start border-warning border-3 mb-2">
                                                <small class="text-muted d-block mb-1">تقييم مرتبط بتعليق:</small>
                                                <p class="mb-0 small">@rating.RestComment.Comment</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Ratings Pagination -->
                        @if (ViewBag.RatingsTotalPages > 1)
                        {
                            <nav aria-label="تنقل التقييمات" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (ViewBag.RatingsPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = (int)ViewBag.RatingsPage - 1, offersPage = ViewBag.OffersPage })">السابق</a>
                                        </li>
                                    }

                                    @for (int i = 1; i <= ViewBag.RatingsTotalPages; i++)
                                    {
                                        <li class="page-item @(i == ViewBag.RatingsPage ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = i, offersPage = ViewBag.OffersPage })">@i</a>
                                        </li>
                                    }

                                    @if (ViewBag.RatingsPage < ViewBag.RatingsTotalPages)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Details", new { id = Model.Id, commentsPage = ViewBag.CommentsPage, ratingsPage = (int)ViewBag.RatingsPage + 1, offersPage = ViewBag.OffersPage })">التالي</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    </div>
                </div>
            </div>
        }

       

        @if (ViewBag.Ratings == null || !((List<RestaurantsCommentsVoting>)ViewBag.Ratings).Any())
        {
            <!-- No Ratings Message -->
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-star text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد تقييمات حتى الآن</h5>
                        
                    </div>
                </div>
            </div>
        }

        @if (ViewBag.Offers == null || !((List<Offer>)ViewBag.Offers).Any())
        {
            <!-- No Offers Message -->
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-gift text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد عروض متاحة حالياً</h5>
                        <p class="text-muted">تابع المطعم لتكون أول من يعلم بالعروض الجديدة</p>
                    </div>
                </div>
            </div>
        }

        <!-- Action Buttons -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                <i class="bi bi-pencil me-1"></i>
                                تعديل المطعم
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="bi bi-trash me-1"></i>
                                حذف المطعم
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Show default icon when image fails to load
        function showDefaultIcon(img) {
            const container = img.parentElement;
            container.innerHTML = `
                <div class="restaurant-icon-placeholder-large rounded-3 d-flex align-items-center justify-content-center shadow-sm" style="width: 120px; height: 120px; background-color: #f8f9fa; border: 2px dashed #dee2e6;">
                    <i class="bi bi-shop text-muted" style="font-size: 3rem;"></i>
                </div>
            `;
        }

        // Copy coordinates to clipboard
        function copyCoordinates() {
            const lat = '@Model.Lat';
            const lng = '@Model.Lng';
            const coordinates = `${lat}, ${lng}`;

            navigator.clipboard.writeText(coordinates).then(function() {
                Swal.fire({
                    icon: 'success',
                    title: 'تم النسخ!',
                    text: 'تم نسخ الإحداثيات إلى الحافظة',
                    timer: 2000,
                    showConfirmButton: false
                });
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'لم يتم نسخ الإحداثيات'
                });
            });
        }

        // Confirm delete
        function confirmDelete() {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'سيتم حذف المطعم "@Model.Name" نهائياً!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'نعم، احذف المطعم',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '@Url.Action("Delete", new { id = Model.Id })';
                }
            });
        }
    </script>
}
