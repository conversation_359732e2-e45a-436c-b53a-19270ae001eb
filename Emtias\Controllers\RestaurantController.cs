using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Emtias.Data;
using Emtias.Models;
using Emtias.Services;
using Microsoft.AspNetCore.Authorization;

namespace Emtias.Controllers
{
    // [Authorize]
    public class RestaurantController : Controller
    {
        private readonly EmtiazDbContext _context;
        private readonly IFileUploadService _fileUploadService;

        public RestaurantController(EmtiazDbContext context, IFileUploadService fileUploadService)
        {
            _context = context;
            _fileUploadService = fileUploadService;
        }

        // GET: Restaurant
        public async Task<IActionResult> Index()
        {
            var appDbContext = _context.Restaurants.Include(r => r.Catgory);
            return View(await appDbContext.ToListAsync());
        }

        // GET: Restaurant/Details/5
        public async Task<IActionResult> Details(int? id, int commentsPage = 1, int ratingsPage = 1, int offersPage = 1)
        {
            if (id == null)
            {
                return NotFound();
            }

            var restaurant = await _context.Restaurants
                .Include(r => r.Catgory)
                .Include(r => r.Products)
                .Include(r => r.OrderItems)
                .Include(r => r.Carts)
                .Include(r => r.Reservations)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (restaurant == null)
            {
                return NotFound();
            }

            // Pagination settings
            const int pageSize = 5;

            // Get comments with pagination (only text comments)
            var commentsQuery = _context.RestaurantsComments
                .Where(c => c.RestId == id && !c.Deleted)
                .Include(c => c.User)
                .OrderByDescending(c => c.CommentDate);

            var totalComments = await commentsQuery.CountAsync();
            var comments = await commentsQuery
                .Skip((commentsPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Get ratings with pagination (independent ratings)
            var ratingsQuery = _context.RestaurantsCommentsVotings
                .Where(v => v.RestComment.RestId == id && !v.Deleted && v.Stars > 0)
                .Include(v => v.User)
                .Include(v => v.RestComment)
                .OrderByDescending(v => v.VotingDate);

            var totalRatings = await ratingsQuery.CountAsync();
            var ratings = await ratingsQuery
                .Skip((ratingsPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Get offers with pagination
            var offersQuery = _context.Offers
                .Where(o => o.RestaurantId == id)
                .Include(o => o.Product)
                .OrderByDescending(o => o.StartDate);

            var totalOffers = await offersQuery.CountAsync();
            var offers = await offersQuery
                .Skip((offersPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Calculate average rating from all ratings
            var allRatings = await _context.RestaurantsCommentsVotings
                .Where(v => v.RestComment.RestId == id && !v.Deleted && v.Stars > 0)
                .Select(v => v.Stars)
                .ToListAsync();

            var averageRating = allRatings.Any() ? allRatings.Average() : 0;

            // Get rating breakdown
            var ratingBreakdown = new Dictionary<int, int>();
            for (int i = 1; i <= 5; i++)
            {
                ratingBreakdown[i] = allRatings.Count(r => r == i);
            }

            // Get likes count for comments
            var commentsWithLikes = await _context.RestaurantsCommentsVotings
                .Where(v => v.RestComment.RestId == id && !v.Deleted && v.LikeComments)
                .GroupBy(v => v.RestCommentId)
                .Select(g => new { CommentId = g.Key, LikesCount = g.Count() })
                .ToListAsync();

            // Pass data to view
            ViewBag.Comments = comments;
            ViewBag.CommentsPage = commentsPage;
            ViewBag.CommentsTotalPages = (int)Math.Ceiling((double)totalComments / pageSize);
            ViewBag.TotalComments = totalComments;
            ViewBag.CommentsWithLikes = commentsWithLikes.ToDictionary(x => x.CommentId, x => x.LikesCount);

            ViewBag.Ratings = ratings;
            ViewBag.RatingsPage = ratingsPage;
            ViewBag.RatingsTotalPages = (int)Math.Ceiling((double)totalRatings / pageSize);
            ViewBag.TotalRatings = totalRatings;

            ViewBag.Offers = offers;
            ViewBag.OffersPage = offersPage;
            ViewBag.OffersTotalPages = (int)Math.Ceiling((double)totalOffers / pageSize);
            ViewBag.TotalOffers = totalOffers;

            ViewBag.AverageRating = averageRating;
            ViewBag.RatingBreakdown = ratingBreakdown;

            return View(restaurant);
        }

        // GET: Restaurant/Create
        public IActionResult Create()
        {
            ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name");
            return View();
        }

        // POST: Restaurant/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,EnName,Details,Address,IconLink,Deleted,CatgoryId,Lat,Lng,ApplicationCommission")] Restaurant restaurant, IFormFile? iconFile = null)
        {
            if (ModelState.IsValid)
            {
                // Handle file upload if a file was provided
                if (iconFile != null && iconFile.Length > 0)
                {
                    var uploadResult = await _fileUploadService.UploadImageAsync(iconFile, "restaurants");
                    if (uploadResult.Success)
                    {
                        restaurant.IconLink = uploadResult.RelativePath;
                    }
                    else
                    {
                        ModelState.AddModelError("", uploadResult.ErrorMessage);
                        ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name", restaurant.CatgoryId);
                        return View(restaurant);
                    }
                }

                _context.Add(restaurant);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"تم إنشاء المطعم '{restaurant.Name}' بنجاح";
                return RedirectToAction(nameof(Index));
            }
            ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name", restaurant.CatgoryId);
            return View(restaurant);
        }

        // GET: Restaurant/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var restaurant = await _context.Restaurants.FindAsync(id);
            if (restaurant == null)
            {
                return NotFound();
            }
            ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name", restaurant.CatgoryId);
            return View(restaurant);
        }

        // POST: Restaurant/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,EnName,Details,Address,IconLink,Deleted,CatgoryId,Lat,Lng,ApplicationCommission")] Restaurant restaurant, IFormFile? iconFile = null)
        {
            if (id != restaurant.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Handle file upload if a file was provided
                    if (iconFile != null && iconFile.Length > 0)
                    {
                        // Get the old icon path to delete it later
                        var oldRestaurant = await _context.Restaurants.AsNoTracking().FirstOrDefaultAsync(r => r.Id == id);
                        var oldIconPath = oldRestaurant?.IconLink;

                        var uploadResult = await _fileUploadService.UploadImageAsync(iconFile, "restaurants");
                        if (uploadResult.Success)
                        {
                            restaurant.IconLink = uploadResult.RelativePath;

                            // Delete old icon file if it exists and is a local file
                            if (!string.IsNullOrEmpty(oldIconPath) && !oldIconPath.StartsWith("http"))
                            {
                                _fileUploadService.DeleteImage(oldIconPath);
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("", uploadResult.ErrorMessage);
                            ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name", restaurant.CatgoryId);
                            return View(restaurant);
                        }
                    }

                    _context.Update(restaurant);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = $"تم تحديث المطعم '{restaurant.Name}' بنجاح";
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    if (!RestaurantExists(restaurant.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        TempData["ErrorMessage"] = "حدث تضارب أثناء التحديث. تم تعديل المطعم من قبل مستخدم آخر. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى";
                        ModelState.AddModelError("", "تم تعديل هذا المطعم من قبل مستخدم آخر. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى");
                    }
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = $"حدث خطأ أثناء تحديث المطعم: {ex.Message}";
                    ModelState.AddModelError("", $"فشل في تحديث المطعم. تفاصيل الخطأ: {ex.Message}");
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["CatgoryId"] = new SelectList(_context.Catgories, "Id", "Name", restaurant.CatgoryId);
            return View(restaurant);
        }

        // GET: Restaurant/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var restaurant = await _context.Restaurants
                .Include(r => r.Catgory)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (restaurant == null)
            {
                return NotFound();
            }

            return View(restaurant);
        }

        // POST: Restaurant/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                Console.WriteLine($"Delete POST method called with ID: {id}");

                if (id <= 0)
                {
                    Console.WriteLine("Invalid ID provided");
                    return NotFound();
                }

                var restaurant = await _context.Restaurants.FindAsync(id);
                Console.WriteLine($"Restaurant found: {restaurant != null}");

                if (restaurant != null)
                {
                    Console.WriteLine($"Deleting restaurant: {restaurant.Name}");

                    // Delete associated icon file if it exists and is a local file
                    if (!string.IsNullOrEmpty(restaurant.IconLink) && !restaurant.IconLink.StartsWith("http"))
                    {
                        Console.WriteLine($"Deleting icon file: {restaurant.IconLink}");
                        _fileUploadService.DeleteImage(restaurant.IconLink);
                    }

                    _context.Restaurants.Remove(restaurant);
                    Console.WriteLine("Restaurant marked for deletion");
                }
                else
                {
                    Console.WriteLine("Restaurant not found in database");
                }

                await _context.SaveChangesAsync();
                Console.WriteLine("Changes saved to database");

                TempData["SuccessMessage"] = "تم حذف المطعم بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Delete method: {ex.Message}");
                TempData["ErrorMessage"] = $"حدث خطأ أثناء حذف المطعم: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        private bool RestaurantExists(int id)
        {
            return _context.Restaurants.Any(e => e.Id == id);
        }

        // API endpoint for uploading icons via AJAX
        [HttpPost]
        public async Task<IActionResult> UploadIcon(IFormFile file)
        {
            try
            {
                var uploadResult = await _fileUploadService.UploadImageAsync(file, "restaurants");
                if (uploadResult.Success)
                {
                    return Json(new { success = true, filePath = uploadResult.RelativePath });
                }
                else
                {
                    return Json(new { success = false, message = uploadResult.ErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"خطأ في رفع الملف: {ex.Message}" });
            }
        }


    }
}
