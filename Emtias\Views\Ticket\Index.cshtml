@model IEnumerable<Emtias.Models.Ticket>

@{
    ViewData["Title"] = "إدارة التذاكر";
}

<div class="pagetitle">
    <h1>إدارة التذاكر</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
            <li class="breadcrumb-item active">إدارة التذاكر</li>
        </ol>
    </nav>
</div>

<!-- Success/Error Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-1"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-1"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<section class="section">
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-lg-3 col-md-6">
            <div class="card info-card sales-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي التذاكر</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-ticket-perforated"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.TotalTickets</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card info-card revenue-card">
                <div class="card-body">
                    <h5 class="card-title">التذاكر المفتوحة</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-ticket-detailed"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.OpenTickets</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card info-card customers-card">
                <div class="card-body">
                    <h5 class="card-title">التذاكر المغلقة</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.ClosedTickets</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card info-card warning-card">
                <div class="card-body">
                    <h5 class="card-title">غير معينة</h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="ps-3">
                            <h6>@ViewBag.UnassignedTickets</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title">قائمة التذاكر</h5>
                        <a href="@Url.Action("Create")" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> إضافة تذكرة جديدة
                        </a>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="statusFilter" class="form-label">تصفية حسب الحالة:</label>
                            <select id="statusFilter" class="form-select" onchange="applyFilters()">
                                <option value="all" selected="@(ViewBag.CurrentStatus == "all" ? "selected" : null)">جميع الحالات</option>
                                <option value="open" selected="@(ViewBag.CurrentStatus == "open" ? "selected" : null)">مفتوحة</option>
                                <option value="closed" selected="@(ViewBag.CurrentStatus == "closed" ? "selected" : null)">مغلقة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="priorityFilter" class="form-label">تصفية حسب الأولوية:</label>
                            <select id="priorityFilter" class="form-select" onchange="applyFilters()">
                                <option value="all" selected="@(ViewBag.CurrentPriority == "all" ? "selected" : null)">جميع الأولويات</option>
                                <option value="high" selected="@(ViewBag.CurrentPriority == "high" ? "selected" : null)">عالية</option>
                                <option value="medium" selected="@(ViewBag.CurrentPriority == "medium" ? "selected" : null)">متوسطة</option>
                                <option value="low" selected="@(ViewBag.CurrentPriority == "low" ? "selected" : null)">منخفضة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">البحث:</label>
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث في العنوان أو الوصف..." onkeyup="searchTickets()">
                        </div>
                    </div>

                    <!-- Tickets Table -->
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped" id="ticketsTable">
                                <thead>
                                    <tr>
                                        <th>رقم التذكرة</th>
                                        <th>العنوان</th>
                                        <th>المستخدم</th>
                                        <th>معين إلى</th>
                                        <th>الحالة</th>
                                        <th>الأولوية</th>
                                        <th>الرسائل</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td><strong>#@item.Id</strong></td>
                                            <td>
                                                <div class="ticket-title">
                                                    @item.Title
                                                    @if (!string.IsNullOrEmpty(item.Description) && item.Description.Length > 50)
                                                    {
                                                        <small class="text-muted d-block">@(item.Description.Substring(0, 50))...</small>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="user-info">
                                                    @if (!string.IsNullOrEmpty(item.User?.Image))
                                                    {
                                                        <img src="@item.User.Image" alt="صورة المستخدم" class="rounded-circle me-2" style="width: 30px; height: 30px;">
                                                    }
                                                    else
                                                    {
                                                        <i class="bi bi-person-circle me-2" style="font-size: 30px;"></i>
                                                    }
                                                    <span>@(item.User?.FullName ?? item.User?.UserName ?? "غير محدد")</span>
                                                </div>
                                            </td>
                                            <td>
                                                @if (item.AssignedToNavigation != null)
                                                {
                                                    <div class="user-info">
                                                        @if (!string.IsNullOrEmpty(item.AssignedToNavigation.Image))
                                                        {
                                                            <img src="@item.AssignedToNavigation.Image" alt="صورة المعين" class="rounded-circle me-2" style="width: 25px; height: 25px;">
                                                        }
                                                        else
                                                        {
                                                            <i class="bi bi-person-badge me-2"></i>
                                                        }
                                                        <span>@(item.AssignedToNavigation.FullName ?? item.AssignedToNavigation.UserName)</span>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">غير معين</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.Status)
                                                {
                                                    <span class="badge bg-success">مفتوحة</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">مغلقة</span>
                                                }
                                            </td>
                                            <td>
                                                @switch (item.Priority?.ToLower())
                                                {
                                                    case "high":
                                                        <span class="badge bg-danger">عالية</span>
                                                        break;
                                                    case "medium":
                                                        <span class="badge bg-warning">متوسطة</span>
                                                        break;
                                                    case "low":
                                                        <span class="badge bg-info">منخفضة</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@item.Priority</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@item.TicketChats.Count</span>
                                            </td>
                                            <td>
                                                <small>@item.CreatedAt.ToString("dd/MM/yyyy")</small><br>
                                                <small class="text-muted">@item.CreatedAt.ToString("HH:mm")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = item.Id })" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    @if (item.AssignedTo != null)
                                                    {
                                                        <a href="@Url.Action("Chat", "TicketChat", new { id = item.Id })" class="btn btn-sm btn-outline-success" title="فتح الدردشة">
                                                            <i class="bi bi-chat-dots"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-ticket-perforated" style="font-size: 4rem; color: #ccc;"></i>
                            <h4 class="mt-3 text-muted">لا توجد تذاكر</h4>
                            <p class="text-muted">لم يتم العثور على أي تذاكر. يمكنك إضافة تذكرة جديدة.</p>
                            <a href="@Url.Action("Create")" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> إضافة تذكرة جديدة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .info-card .card-icon {
        color: #012970;
        background: #f6f9ff;
    }
    
    .sales-card .card-icon {
        color: #4154f1;
        background: #f6f6fe;
    }
    
    .revenue-card .card-icon {
        color: #2eca6a;
        background: #e0f8e9;
    }
    
    .customers-card .card-icon {
        color: #ff771d;
        background: #ffecdf;
    }
    
    .warning-card .card-icon {
        color: #ff771d;
        background: #fff2e6;
    }
    
    .ticket-title {
        max-width: 200px;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        max-width: 150px;
    }
    
    .user-info span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>

<script>
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Apply filters
    function applyFilters() {
        const status = document.getElementById('statusFilter').value;
        const priority = document.getElementById('priorityFilter').value;
        
        const url = new URL(window.location);
        url.searchParams.set('status', status);
        url.searchParams.set('priority', priority);
        
        window.location.href = url.toString();
    }

    // Search functionality
    function searchTickets() {
        const input = document.getElementById('searchInput');
        const filter = input.value.toLowerCase();
        const table = document.getElementById('ticketsTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const titleCell = rows[i].getElementsByTagName('td')[1];
            if (titleCell) {
                const titleText = titleCell.textContent || titleCell.innerText;
                if (titleText.toLowerCase().indexOf(filter) > -1) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }
</script>
