@model Emtias.Models.Content

@{
    ViewData["Title"] = "تفاصيل المحتوى";
}

<!-- Page Title -->
<div class="pagetitle">
    <h1>تفاصيل المحتوى</h1>
    <nav>
        <ol class="breadcrumb">
            <li class=""><a asp-action="Index" asp-controller="Home">الرئيسية</a></li>
            <li class="breadcrumb-item"><a asp-action="Index">المحتوى</a></li>
            <li class="breadcrumb-item active">التفاصيل</li>
        </ol>
    </nav>
</div>

<!-- Main Content -->
<section class="section">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header Card -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-2">@Model.Title</h5>
                            <p class="text-muted mb-0">
                                <small>
                                    <i class="bi bi-hash"></i> رقم المحتوى: <strong>@Model.Id</strong>
                                </small>
                            </p>
                        </div>
                        <div class="btn-group" role="group">
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="bi bi-pencil me-1"></i>
                                تعديل
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="bi bi-trash me-1"></i>
                                حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dates Information Card -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-calendar-event me-2"></i>
                        معلومات التواريخ
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">
                                        <i class="bi bi-calendar-plus me-2"></i>
                                        تاريخ الإنشاء
                                    </h6>
                                    <p class="card-text mb-1">
                                        <i class="bi bi-calendar me-1"></i>
                                        @Model.CreatedAt.ToString("dd/MM/yyyy")
                                    </p>
                                    <p class="card-text text-muted">
                                        <i class="bi bi-clock me-1"></i>
                                        @Model.CreatedAt.ToString("hh:mm tt")
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        آخر تحديث
                                    </h6>
                                    @if (Model.UpdatedAt.HasValue)
                                    {
                                        <p class="card-text mb-1">
                                            <i class="bi bi-calendar me-1"></i>
                                            @Model.UpdatedAt.Value.ToString("dd/MM/yyyy")
                                        </p>
                                        <p class="card-text text-muted">
                                            <i class="bi bi-clock me-1"></i>
                                            @Model.UpdatedAt.Value.ToString("hh:mm tt")
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="card-text">
                                            <span class="badge bg-secondary">لم يتم التحديث بعد</span>
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Display Card -->
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-file-text me-2"></i>
                        المحتوى
                    </h6>
                    <hr>
                    <div class="content-display p-3" style="background-color: #f8f9fa; border-radius: 8px; min-height: 200px;">
                        @Html.Raw(Model.Content1)
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            العودة للقائمة
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="bi bi-pencil me-1"></i>
                                تعديل المحتوى
                            </a>
                            <button type="button" class="btn btn-info" onclick="printContent()">
                                <i class="bi bi-printer me-1"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <style>
        .content-display {
            line-height: 1.8;
            font-size: 16px;
        }

        .content-display h1,
        .content-display h2,
        .content-display h3,
        .content-display h4,
        .content-display h5,
        .content-display h6 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        .content-display p {
            margin-bottom: 1rem;
        }

        .content-display ul,
        .content-display ol {
            margin-bottom: 1rem;
            padding-right: 2rem;
        }

        .content-display li {
            margin-bottom: 0.5rem;
        }

        .content-display img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .content-display table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }

        .content-display table th,
        .content-display table td {
            padding: 0.75rem;
            border: 1px solid #dee2e6;
        }

        .content-display table th {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .content-display blockquote {
            border-right: 4px solid #0d6efd;
            padding-right: 1rem;
            margin: 1rem 0;
            color: #6c757d;
            font-style: italic;
        }

        .content-display a {
            color: #0d6efd;
            text-decoration: none;
        }

        .content-display a:hover {
            text-decoration: underline;
        }

        .card {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            color: #012970;
            font-weight: 600;
        }

        @@media print {
            .pagetitle,
            .btn,
            .btn-group,
            nav,
            .card:last-child {
                display: none !important;
            }

            .content-display {
                background-color: white !important;
            }
        }
    </style>

    <script>
        // Print content function
        function printContent() {
            window.print();
        }

        // Add smooth scroll behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to cards
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s both`;
            });
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @@keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
}

