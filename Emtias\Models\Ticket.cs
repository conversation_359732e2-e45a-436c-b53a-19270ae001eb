﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Emtias.Models;

public partial class Ticket
{
    public int Id { get; set; }

    [Required(ErrorMessage = "عنوان التذكرة مطلوب")]
    [StringLength(200, ErrorMessage = "عنوان التذكرة يجب أن يكون أقل من 200 حرف")]
    public string Title { get; set; } = null!;

    [Required(ErrorMessage = "وصف التذكرة مطلوب")]
    [StringLength(2000, ErrorMessage = "وصف التذكرة يجب أن يكون أقل من 2000 حرف")]
    public string? Description { get; set; }

    public bool Status { get; set; }

    [Required(ErrorMessage = "أولوية التذكرة مطلوبة")]
    public string Priority { get; set; } = null!;

    [Required(ErrorMessage = "يجب اختيار المستخدم")]
    public string UserId { get; set; } = null!;

    public string? AssignedTo { get; set; }

    public int? Vote { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public virtual AspNetUser? AssignedToNavigation { get; set; }

    public virtual ICollection<TicketChat> TicketChats { get; set; } = new List<TicketChat>();

    public virtual AspNetUser User { get; set; } = null!;
}
