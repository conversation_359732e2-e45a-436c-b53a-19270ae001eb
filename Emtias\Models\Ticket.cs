﻿using System;
using System.Collections.Generic;

namespace Emtias.Models;

public partial class Ticket
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public bool Status { get; set; }

    public string Priority { get; set; } = null!;

    public string UserId { get; set; } = null!;

    public string? AssignedTo { get; set; }

    public int? Vote { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public virtual AspNetUser? AssignedToNavigation { get; set; }

    public virtual ICollection<TicketChat> TicketChats { get; set; } = new List<TicketChat>();

    public virtual AspNetUser? User { get; set; }
}
